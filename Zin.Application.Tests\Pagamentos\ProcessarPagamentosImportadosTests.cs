using AutoMapper;
using Moq;
using System.Linq.Expressions;
using Zin.Application.DTOs.Pagamentos;
using Zin.Application.Services.ZinPag;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Tests.Pagamentos
{
    public class ProcessarPagamentosImportadosTests
    {
        private readonly Mock<IAtivoRepository> _ativoRepositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<IPagamentoRepository> _pagamentoRepositoryMock;
        private readonly Mock<IDocumentoRepository> _documentoRepositoryMock;
        private readonly Mock<IPessoaRepository> _pessoaRepositoryMock;
        private readonly Mock<IDocumentoPagamentoRepository> _documentoPagamentoRepositoryMock;
        private readonly Mock<ILiquidacaoPagamentoRepository> _liquidacaoPagamentoRepositoryMock;
        private readonly PagamentoService _sut;

        public ProcessarPagamentosImportadosTests()
        {
            _ativoRepositoryMock = new Mock<IAtivoRepository>();
            _mapperMock = new Mock<IMapper>();
            _pagamentoRepositoryMock = new Mock<IPagamentoRepository>();
            _documentoRepositoryMock = new Mock<IDocumentoRepository>();
            _pessoaRepositoryMock = new Mock<IPessoaRepository>();
            _documentoPagamentoRepositoryMock = new Mock<IDocumentoPagamentoRepository>();
            _liquidacaoPagamentoRepositoryMock = new Mock<ILiquidacaoPagamentoRepository>();

            _sut = new PagamentoService(
                _ativoRepositoryMock.Object,
                _mapperMock.Object,
                _pagamentoRepositoryMock.Object,
                _documentoRepositoryMock.Object,
                _pessoaRepositoryMock.Object,
                _documentoPagamentoRepositoryMock.Object,
                _liquidacaoPagamentoRepositoryMock.Object
            );
        }

        private static PagamentoExcelDTO CriarPagamentoExcelDtoValido(string numeroNf = "NF-001", string valor = "100.00", string cnpj = "12345678000195")
        {
            return new PagamentoExcelDTO
            {
                NumeroNF = numeroNf,
                ValorPago = valor,
                DataPagamento = "2024-07-24",
                CnpjCpfFavorecido = cnpj
            };
        }

        [Fact]
        public async Task ProcessarPagamentos_QuandoDadosSaoValidos_DeveProcessarComSucesso()
        {
            // Arrange
            var pagamentoDto = CriarPagamentoExcelDtoValido();
            var notaFiscal = new Documento { Id = 1, Numero = pagamentoDto.NumeroNF, ValorTotal = 100, TipoDocumento = TipoDocumento.NotaFiscalVenda };
            var pessoa = new Pessoa { Id = 1 };

            _documentoRepositoryMock.Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Documento, bool>>>()))
                                    .ReturnsAsync(new List<Documento> { notaFiscal });
            _pessoaRepositoryMock.Setup(r => r.BuscarPorDocumentoAsync("12345678000195")).ReturnsAsync(pessoa);
            _pagamentoRepositoryMock.Setup(r => r.ObterPorDocumentoIdAsync(notaFiscal.Id)).ReturnsAsync((Pagamento)null);
            _documentoRepositoryMock.Setup(r => r.ObterPorIdAsync(notaFiscal.Id)).ReturnsAsync(notaFiscal);
            _pagamentoRepositoryMock.Setup(r => r.InserirAsync(It.IsAny<Pagamento>())).ReturnsAsync(1);

            // Act
            var resultado = await _sut.ProcessarPagamentosImportadosAsync(new List<PagamentoExcelDTO> { pagamentoDto });

            // Assert
            Assert.Single(resultado.PagamentosComSucesso);
            Assert.Empty(resultado.PagamentosComErro);
            Assert.Equal("Pagamentos processados com sucesso.", resultado.Mensagem);
            Assert.Null(resultado.PagamentosComSucesso[0].Erro);
            _pagamentoRepositoryMock.Verify(r => r.InserirAsync(It.IsAny<Pagamento>()), Times.Once);
            _liquidacaoPagamentoRepositoryMock.Verify(r => r.InserirAsync(It.IsAny<LiquidacaoPagamento>()), Times.Once);
        }

        [Fact]
        public async Task ProcessarPagamentos_QuandoNotaFiscalNaoExiste_DeveRetornarErro()
        {
            // Arrange
            var pagamentoDto = CriarPagamentoExcelDtoValido();
            _documentoRepositoryMock.Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Documento, bool>>>()))
                                    .ReturnsAsync(new List<Documento>());

            // Act
            var resultado = await _sut.ProcessarPagamentosImportadosAsync(new List<PagamentoExcelDTO> { pagamentoDto });

            // Assert
            Assert.Empty(resultado.PagamentosComSucesso);
            Assert.Single(resultado.PagamentosComErro);
            Assert.Equal("Nenhum pagamento foi processado.", resultado.Mensagem);
            Assert.Equal("Nota fiscal não encontrada.", resultado.PagamentosComErro[0].Erro);
        }

        [Fact]
        public async Task ProcessarPagamentos_QuandoFavorecidoNaoExiste_DeveRetornarErro()
        {
            // Arrange
            var pagamentoDto = CriarPagamentoExcelDtoValido();
            var notaFiscal = new Documento { Id = 1, Numero = pagamentoDto.NumeroNF, ValorTotal = 100, TipoDocumento = TipoDocumento.NotaFiscalVenda };

            _documentoRepositoryMock.Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Documento, bool>>>()))
                                    .ReturnsAsync(new List<Documento> { notaFiscal });
            _pessoaRepositoryMock.Setup(r => r.BuscarPorDocumentoAsync(It.IsAny<string>())).ReturnsAsync((Pessoa)null);

            // Act
            var resultado = await _sut.ProcessarPagamentosImportadosAsync(new List<PagamentoExcelDTO> { pagamentoDto });

            // Assert
            Assert.Empty(resultado.PagamentosComSucesso);
            Assert.Single(resultado.PagamentosComErro);
            Assert.Equal("Favorecido não encontrado.", resultado.PagamentosComErro[0].Erro);
        }

        [Fact]
        public async Task ProcessarPagamentos_QuandoValorPagoInvalido_DeveRetornarErroDeConversao()
        {
            // Arrange
            var pagamentoDto = CriarPagamentoExcelDtoValido(valor: "cem reais");
            var notaFiscal = new Documento { Id = 1, Numero = pagamentoDto.NumeroNF, ValorTotal = 100, TipoDocumento = TipoDocumento.NotaFiscalVenda };
            var pessoa = new Pessoa { Id = 1 };

            _documentoRepositoryMock.Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Documento, bool>>>()))
                                    .ReturnsAsync(new List<Documento> { notaFiscal });
            _pessoaRepositoryMock.Setup(r => r.BuscarPorDocumentoAsync("12345678000195")).ReturnsAsync(pessoa);

            // Act
            var resultado = await _sut.ProcessarPagamentosImportadosAsync(new List<PagamentoExcelDTO> { pagamentoDto });

            // Assert
            Assert.Empty(resultado.PagamentosComSucesso);
            Assert.Single(resultado.PagamentosComErro);
            Assert.Equal("Erro de conversão de valores.", resultado.PagamentosComErro[0].Erro);
        }

        [Fact]
        public async Task ProcessarPagamentos_QuandoDataPagamentoInvalida_DeveRetornarErroDeConversao()
        {
            // Arrange
            var pagamentoDto = CriarPagamentoExcelDtoValido();
            pagamentoDto.DataPagamento = "data-invalida";
            var notaFiscal = new Documento { Id = 1, Numero = pagamentoDto.NumeroNF, ValorTotal = 100, TipoDocumento = TipoDocumento.NotaFiscalVenda };
            var pessoa = new Pessoa { Id = 1 };

            _documentoRepositoryMock.Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Documento, bool>>>()))
                                    .ReturnsAsync(new List<Documento> { notaFiscal });
            _pessoaRepositoryMock.Setup(r => r.BuscarPorDocumentoAsync("12345678000195")).ReturnsAsync(pessoa);

            // Act
            var resultado = await _sut.ProcessarPagamentosImportadosAsync(new List<PagamentoExcelDTO> { pagamentoDto });

            // Assert
            Assert.Empty(resultado.PagamentosComSucesso);
            Assert.Single(resultado.PagamentosComErro);
            Assert.Equal("Erro de conversão de valores.", resultado.PagamentosComErro[0].Erro);
        }

        [Fact]
        public async Task ProcessarPagamentos_QuandoValorPagoExcedeValorDaNota_DeveRetornarErro()
        {
            // Arrange
            var pagamentoDto = CriarPagamentoExcelDtoValido(valor: "150.00");
            var notaFiscal = new Documento { Id = 1, Numero = pagamentoDto.NumeroNF, ValorTotal = 100, TipoDocumento = TipoDocumento.NotaFiscalVenda };
            var pessoa = new Pessoa { Id = 1 };

            _documentoRepositoryMock.Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Documento, bool>>>()))
                                    .ReturnsAsync(new List<Documento> { notaFiscal });
            _pessoaRepositoryMock.Setup(r => r.BuscarPorDocumentoAsync("12345678000195")).ReturnsAsync(pessoa);
            _pagamentoRepositoryMock.Setup(r => r.ObterPorDocumentoIdAsync(notaFiscal.Id)).ReturnsAsync((Pagamento)null);
            _documentoRepositoryMock.Setup(r => r.ObterPorIdAsync(notaFiscal.Id)).ReturnsAsync(notaFiscal);

            // Act
            var resultado = await _sut.ProcessarPagamentosImportadosAsync(new List<PagamentoExcelDTO> { pagamentoDto });

            // Assert
            Assert.Empty(resultado.PagamentosComSucesso);
            Assert.Single(resultado.PagamentosComErro);
            Assert.StartsWith("O valor do pagamento (150,00) para a nota fiscal NF-001 excede o valor total da nota (100).", resultado.PagamentosComErro[0].Erro);
        }

        [Fact]
        public async Task ProcessarPagamentos_QuandoPagamentoParcialJaExiste_DeveSomarValorEAtualizarPagamento()
        {
            // Arrange
            var pagamentoDto = CriarPagamentoExcelDtoValido(valor: "50.00");
            var notaFiscal = new Documento { Id = 1, Numero = pagamentoDto.NumeroNF, ValorTotal = 100, TipoDocumento = TipoDocumento.NotaFiscalVenda };
            var pessoa = new Pessoa { Id = 1 };
            var pagamentoExistente = new Pagamento { Id = 1, IdPessoaBeneficiaria = 1, Valor = 50, StatusPagamento = StatusPagamento.PagoParcialmente };

            _documentoRepositoryMock.Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Documento, bool>>>()))
                                    .ReturnsAsync(new List<Documento> { notaFiscal });
            _pessoaRepositoryMock.Setup(r => r.BuscarPorDocumentoAsync("12345678000195")).ReturnsAsync(pessoa);
            _pagamentoRepositoryMock.Setup(r => r.ObterPorDocumentoIdAsync(notaFiscal.Id)).ReturnsAsync(pagamentoExistente);
            _documentoRepositoryMock.Setup(r => r.ObterPorIdAsync(notaFiscal.Id)).ReturnsAsync(notaFiscal);

            // Act
            var resultado = await _sut.ProcessarPagamentosImportadosAsync(new List<PagamentoExcelDTO> { pagamentoDto });

            // Assert
            Assert.Single(resultado.PagamentosComSucesso);
            Assert.Empty(resultado.PagamentosComErro);
            _pagamentoRepositoryMock.Verify(r => r.AtualizarAsync(It.Is<Pagamento>(p => p.Valor == 100 && p.StatusPagamento == StatusPagamento.Pago)), Times.Once);
            _liquidacaoPagamentoRepositoryMock.Verify(r => r.InserirAsync(It.IsAny<LiquidacaoPagamento>()), Times.Once);
        }

        [Fact]
        public async Task ProcessarPagamentos_QuandoSomaParcialNaoAlcancaValorTotal_DeveManterStatusParcial()
        {
            // Arrange
            var pagamentoDto = CriarPagamentoExcelDtoValido(valor: "30.00");
            var notaFiscal = new Documento { Id = 1, Numero = pagamentoDto.NumeroNF, ValorTotal = 100, TipoDocumento = TipoDocumento.NotaFiscalVenda };
            var pessoa = new Pessoa { Id = 1 };
            var pagamentoExistente = new Pagamento { Id = 1, IdPessoaBeneficiaria = 1, Valor = 30, StatusPagamento = StatusPagamento.PagoParcialmente };

            _documentoRepositoryMock.Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Documento, bool>>>()))
                                    .ReturnsAsync(new List<Documento> { notaFiscal });
            _pessoaRepositoryMock.Setup(r => r.BuscarPorDocumentoAsync("12345678000195")).ReturnsAsync(pessoa);
            _pagamentoRepositoryMock.Setup(r => r.ObterPorDocumentoIdAsync(notaFiscal.Id)).ReturnsAsync(pagamentoExistente);
            _documentoRepositoryMock.Setup(r => r.ObterPorIdAsync(notaFiscal.Id)).ReturnsAsync(notaFiscal);

            // Act
            var resultado = await _sut.ProcessarPagamentosImportadosAsync(new List<PagamentoExcelDTO> { pagamentoDto });

            // Assert
            Assert.Single(resultado.PagamentosComSucesso);
            Assert.Empty(resultado.PagamentosComErro);
            _pagamentoRepositoryMock.Verify(r => r.AtualizarAsync(
                It.Is<Pagamento>(p => p.Valor == 60 && p.StatusPagamento == StatusPagamento.PagoParcialmente)), Times.Once);
        }

        [Fact]
        public async Task ProcessarPagamentos_QuandoUmComSucessoEOutroComErro_DeveRetornarMensagemParcial()
        {
            // Arrange
            var dtoValido = CriarPagamentoExcelDtoValido(numeroNf: "NF-001", valor: "100.00");
            var dtoInvalido = CriarPagamentoExcelDtoValido(numeroNf: "NF-404");
            var notaFiscal = new Documento { Id = 1, Numero = dtoValido.NumeroNF, ValorTotal = 100, TipoDocumento = TipoDocumento.NotaFiscalVenda };
            var pessoa = new Pessoa { Id = 1 };

            _documentoRepositoryMock.Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Documento, bool>>>()))
                .ReturnsAsync((Expression<Func<Documento, bool>> predicado) =>
                {
                    if (predicado.Compile()(notaFiscal)) return new List<Documento> { notaFiscal };
                    return new List<Documento>();
                });

            _pessoaRepositoryMock.Setup(r => r.BuscarPorDocumentoAsync(It.IsAny<string>())).ReturnsAsync(pessoa);
            _pagamentoRepositoryMock.Setup(r => r.ObterPorDocumentoIdAsync(notaFiscal.Id)).ReturnsAsync((Pagamento)null);
            _documentoRepositoryMock.Setup(r => r.ObterPorIdAsync(notaFiscal.Id)).ReturnsAsync(notaFiscal);
            _pagamentoRepositoryMock.Setup(r => r.InserirAsync(It.IsAny<Pagamento>())).ReturnsAsync(1);

            // Act
            var resultado = await _sut.ProcessarPagamentosImportadosAsync(new List<PagamentoExcelDTO> { dtoValido, dtoInvalido });

            // Assert
            Assert.Single(resultado.PagamentosComSucesso);
            Assert.Single(resultado.PagamentosComErro);
            Assert.Equal("Pagamentos processados parcialmente.", resultado.Mensagem);
        }

        [Fact]
        public async Task ProcessarPagamentos_QuandoOcorreExcecaoDuranteProcessamento_DeveCapturarErro()
        {
            // Arrange
            var dto = CriarPagamentoExcelDtoValido();
            var notaFiscal = new Documento { Id = 1, Numero = dto.NumeroNF, ValorTotal = 100, TipoDocumento = TipoDocumento.NotaFiscalVenda };
            var pessoa = new Pessoa { Id = 1 };

            _documentoRepositoryMock.Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Documento, bool>>>()))
                                    .ReturnsAsync(new List<Documento> { notaFiscal });
            _pessoaRepositoryMock.Setup(r => r.BuscarPorDocumentoAsync("12345678000195")).ReturnsAsync(pessoa);
            _pagamentoRepositoryMock.Setup(r => r.ObterPorDocumentoIdAsync(notaFiscal.Id)).ReturnsAsync((Pagamento)null);
            _documentoRepositoryMock.Setup(r => r.ObterPorIdAsync(notaFiscal.Id)).ReturnsAsync(notaFiscal);
            _pagamentoRepositoryMock.Setup(r => r.InserirAsync(It.IsAny<Pagamento>())).ThrowsAsync(new Exception("explodiu"));

            // Act
            var resultado = await _sut.ProcessarPagamentosImportadosAsync(new List<PagamentoExcelDTO> { dto });

            // Assert
            Assert.Empty(resultado.PagamentosComSucesso);
            Assert.Single(resultado.PagamentosComErro);
            Assert.StartsWith("Erro ao processar pagamento.", resultado.PagamentosComErro[0].Erro);
        }
    }
}
