﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Entidades.ZinPag.Processamento
{
    [Table("registro_processamento_item_versao", Schema = "zinpag")]
    public class RegistroProcessamentoItemVersao
    {
        [Key]
        [Column("id_registro_processamento_item_versao")]
        public int Id { get; set; }

        [Column("id_item_versao")]
        public int IdItemVersao { get; set; }

        [Column("tipo")]
        public TipoProcessamento Tipo { get; set; }

        [Column("status")]
        public StatusProcessamento Status { get; set; }

        [Column("data_atualizacao")]
        public DateTime? DataAtualizacao { get; set; }

        [Column("divergencia")]
        public Divergencia? Divergencia { get; set; }

        [Column("condicao")]
        public Condicao? Condicao { get; set; }

        [Column("detalhe")]
        public string? Detalhe { get; set; }

        [Column("decisao")]
        public DecisaoDivergencia? Decisao { get; set; }

        [Column("usuario")]
        public string? Usuario { get; set; }

        [ForeignKey("IdItemVersao")]
        public ItemVersao? ItemVersao { get; set; }
    }
}
