using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface IAgregadorRepository : IRepositoryBase<Agregador, int>
    {
        Task AtualizarStatusAsync(int idAgregador, StatusProcessamento divergente);
        Task<Agregador?> BuscarPorNumeroAsync(string numero);
        Task<IEnumerable<Agregador>> BuscarPorStatusAsync(StatusProcessamento status);
    }
}