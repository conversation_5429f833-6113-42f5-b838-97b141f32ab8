﻿using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Application.Helpers;
using Zin.Application.Specifications.ItensVersoes.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;

namespace Zin.Application.Specifications.ItensVersoes
{
    /// <summary>
    /// Specification para verificar se é possível criar uma versão de exclusão de um item
    /// </summary>
    public class ExclusaoComAutorizacaoSpecification : IItemVersaoSpecification
    {
        public DeveCriarNovaVersaoResponse IsSatisfiedBy(ICollection<ItemVersao> todasVersoes, ImportarItensDTO itemDto)
        {
            bool deveCriarNovaVersao = false;
            ItemVersao? versaoAnterior = null;

            if (itemDto.TipoMovimentoItem == TipoMovimentoItem.Exclusao)
            {
                if (todasVersoes == null || todasVersoes.Count <= 0)
                {
                    // Se não há versões anteriores do item, não é possível criar uma versão de exclusão
                    throw new InvalidOperationException(ItemVersaoSpecificationHelper.MensagemErroExclusaoSemAutorizacao(itemDto));
                }

                // Filtra as versões anteriores do item com a mesma autorização e fornecedor
                // e ordena por data de criação decrescente
                var todasVersoesMesmoFornecedorEAutorizacao = ItemVersaoSpecificationHelper
                    .ObterTodasVersoesMesmoFornecedorEAutorizacao(
                        todasVersoes,
                        itemDto.DataAutorizacao,
                        itemDto.CnpjFornecedor);

                if (todasVersoesMesmoFornecedorEAutorizacao == null
                    || !todasVersoesMesmoFornecedorEAutorizacao.Any())
                {
                    // Não há versões anteriores do item com a mesma autorização e fornecedor,
                    // não é possível criar uma versão de exclusão
                    throw new InvalidOperationException(ItemVersaoSpecificationHelper.MensagemErroExclusaoSemAutorizacao(itemDto));
                }

                // Verifica se existe uma versão de autorização para o mesmo fornecedor e data de autorização
                // Como a ordem é decrescente, a primeira versão encontrada será a mais recente
                var versaoDeAutorizacao = todasVersoesMesmoFornecedorEAutorizacao!
                    .OrderByDescending(v => v.DataCriacao)
                    .FirstOrDefault(v => v.TipoMovimento == TipoMovimentoItem.Autorizacao);

                if (versaoDeAutorizacao != null)
                {
                    // Existe uma versão de autorização para o mesmo fornecedor,
                    // com a mesma data de autorização, criar nova versão
                    deveCriarNovaVersao = true;
                    versaoAnterior = versaoDeAutorizacao;
                }
                else
                {
                    // Não existe uma versão de autorização para o mesmo fornecedor,
                    throw new InvalidOperationException(ItemVersaoSpecificationHelper.MensagemErroExclusaoSemAutorizacao(itemDto));
                }
            }

            return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(deveCriarNovaVersao, versaoAnterior);
        }
    }
}
