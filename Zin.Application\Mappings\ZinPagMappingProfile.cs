﻿using AutoMapper;
using Zin.Application.DTOs.Agregadores;
using Zin.Application.DTOs.Documentos;
using Zin.Application.DTOs.Movimentacoes;
using Zin.Domain.Entidades.Cadastros.DadosBancarios;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;

namespace Zin.Application.Mappings
{
    public class ZinPagMappingProfile : Profile
    {
        public ZinPagMappingProfile()
        {
            CreateMap<Agregador, AgregadorDto>().ReverseMap();

            CreateMap<Movimentacao, ObterMovimentacaoDetalhesDto>()
                .ForMember(dest => dest.IdMovimentacao, opt => opt.MapFrom(src => src.IdMovimentacao))
                .ForMember(dest => dest.IdAgregador, opt => opt.MapFrom(src => src.Agregador.Id))
                .ForMember(dest => dest.NumeroSinistro, opt => opt.MapFrom(src => src.Agregador.Numero));

            CreateMap<Pessoa, FornecedorDto>()
                .ForMember(dest => dest.DadosBancarios, opt => opt.MapFrom(src => src.DadosBancarios.FirstOrDefault()));

            CreateMap<PessoaJuridica, FornecedorDto>()
                .IncludeBase<Pessoa, FornecedorDto>()
                .ForMember(dest => dest.NomeFantasia, opt => opt.MapFrom(src => src.NomeFantasia))
                .ForMember(dest => dest.RazaoSocial, opt => opt.MapFrom(src => src.RazaoSocial))
                .ForMember(dest => dest.Cnpj, opt => opt.MapFrom(src => src.Cnpj));

            CreateMap<PessoaFisica, FornecedorDto>()
                .IncludeBase<Pessoa, FornecedorDto>()
                .ForMember(dest => dest.NomeFantasia, opt => opt.MapFrom(src => src.Nome))
                .ForMember(dest => dest.RazaoSocial, opt => opt.MapFrom(src => src.Nome))
                .ForMember(dest => dest.Cnpj, opt => opt.MapFrom(src => src.Cpf));

            CreateMap<PessoaJuridica, OficinaDto>()
                .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NomeFantasia));

            CreateMap<Veiculo, AtivoDto>()
                .ForMember(dest => dest.Ano, opt => opt.MapFrom(src => src.AnoFabricacao))
                .ForMember(dest => dest.SituacaoReparo, opt => opt.MapFrom(src => src.StatusVeiculo.ToString()));

            CreateMap<DadoBancario, DadosBancariosDto>();

            CreateMap<Documento, DocumentoDto>()
                .ForMember(dest => dest.Tipo, opt => opt.MapFrom(src => src.TipoDocumento))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => src.ValorTotal))
                .ForMember(dest => dest.Serie, opt => opt.MapFrom(src => ""))
                .ForMember(dest => dest.Chave, opt => opt.MapFrom(src => ""));
        }
    }
}
