﻿using AutoMapper;
using Zin.Application.DTOs.Configuracao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Domain.Entidades.Cadastros.Condicoes;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Application.Mappings
{
    public class ConfiguracaoMappingProfile : Profile
    {
        public ConfiguracaoMappingProfile()
        {
            // Entidade para DTO (inclui lista de regras)
            CreateMap<Configuracao, ConfiguracaoDto>();

            // Criação: DTO para Entidade (inclui lista de regras)
            CreateMap<CriaConfiguracaoDto, Configuracao>();

            // Atualização: DTO para Entidade
            CreateMap<AtualizaConfiguracaoDto, Configuracao>();
        }
    }
}
