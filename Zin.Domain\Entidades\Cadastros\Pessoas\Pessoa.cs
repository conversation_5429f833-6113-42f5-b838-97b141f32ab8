﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.DadosBancarios;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.Cadastros.Pessoas
{
    [Table("pessoas", Schema = "cadastros")]
    public class Pessoa
    {
        [Key]
        [Column("id_pessoa")]
        public int Id { get; set; }

        [Column("tipo_pessoa")]
        public TipoPessoa TipoPessoa { get; set; }

        public ICollection<DadoBancario> DadosBancarios { get; set; } = [];

        public ICollection<PessoaContato> PessoasContatos { get; set; } = [];
        public ICollection<PessoaEndereco> PessoasEnderecos { get; set; } = [];
    }
}
