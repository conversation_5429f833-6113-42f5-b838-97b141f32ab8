﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Zin.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class CriaConfiguracaoeRegras : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "configuracoes",
                schema: "zinpag",
                columns: table => new
                {
                    id_configuracao = table.Column<Guid>(type: "uuid", nullable: false),
                    nome = table.Column<string>(type: "text", nullable: false),
                    tipo_processamento = table.Column<int>(type: "integer", nullable: false),
                    tipo_configuracao = table.Column<int>(type: "integer", nullable: false),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    criado_em = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    atualizado_em = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    criado_por = table.Column<string>(type: "text", nullable: false),
                    alterado_por = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_configuracoes", x => x.id_configuracao);
                });

            migrationBuilder.CreateTable(
                name: "regras",
                schema: "zinpag",
                columns: table => new
                {
                    id_regra = table.Column<Guid>(type: "uuid", nullable: false),
                    id_configuracao = table.Column<Guid>(type: "uuid", nullable: false),
                    nome = table.Column<string>(type: "text", nullable: false),
                    valor = table.Column<string>(type: "text", nullable: false),
                    tipagem = table.Column<string>(type: "text", nullable: false),
                    operador = table.Column<int>(type: "integer", nullable: false),
                    ativo = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_regras", x => x.id_regra);
                    table.ForeignKey(
                        name: "FK_regras_configuracoes_id_configuracao",
                        column: x => x.id_configuracao,
                        principalSchema: "zinpag",
                        principalTable: "configuracoes",
                        principalColumn: "id_configuracao",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_regras_id_configuracao",
                schema: "zinpag",
                table: "regras",
                column: "id_configuracao");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "regras",
                schema: "zinpag");

            migrationBuilder.DropTable(
                name: "configuracoes",
                schema: "zinpag");
        }
    }
}
