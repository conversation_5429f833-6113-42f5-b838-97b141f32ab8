﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Entidades.Cadastros.Condicoes
{
    [Table("configuracoes", Schema = "zinpag")]
    public class Configuracao
    {
        [Key]
        [Column("id_configuracao")]
        public Guid Id { get; set; }

        [Column("nome")]
        public required string Nome { get; set; }

        [Column("tipo_processamento")]
        public required TipoProcessamento TipoProcessamento { get; set; }

        [Column("tipo_configuracao")]
        public required TipoConfiguracao TipoConfiguracao { get; set; }

        [Column("ativo")]
        public required bool Ativo { get; set; }

        [Column("criado_em")]
        public required DateTime CriadoEm { get; set; }

        [Column("atualizado_em")]
        public DateTime? AtualizadoEm { get; set; }

        [Column("criado_por")]
        public required string CriadoPor { get; set; }

        [Column("alterado_por")]
        public string? AlteradoPor { get; set; }

        public ICollection<Regra> Regras { get; set; } = [];

    }
}
