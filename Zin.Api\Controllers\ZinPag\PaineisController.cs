﻿using Microsoft.AspNetCore.Mvc;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Api.Controllers.ZinPag
{
    public class PaineisController : ControllerBase
    {
        [HttpGet]
        [Route("painel-principal")]
        public async Task<IActionResult> ObterPainelPrincipalValorAsync([FromServices] IPaineisService paineisMockService)
        {
            try
            {
                var painel = await paineisMockService.ObterPainelPrincipalAsync();
                return Ok(painel);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno: {ex.Message}");
            }
        }
    }
}
