﻿using AutoMapper;
using Zin.Application.DTOs.Configuracao;
using Zin.Domain.Entidades.Cadastros.Condicoes;

namespace Zin.Application.Mappings
{
    public class RegraMappingProfile : Profile
    {
        public RegraMappingProfile()
        {
            // Entidade para DTO
            CreateMap<Regra, RegraDto>();

            // Criação: DTO para Entidade
            CreateMap<CriaRegraDto, Regra>();

            // Atualização: DTO para Entidade
            CreateMap<AtualizaRegraDto, Regra>();
        }
    }
}
