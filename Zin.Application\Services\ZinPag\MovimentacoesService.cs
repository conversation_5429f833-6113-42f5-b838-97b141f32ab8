﻿using AutoMapper;
using Zin.Application.DTOs.Documentos;
using Zin.Application.DTOs.Movimentacoes;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class MovimentacoesService(
        IAgregadorRepository agregadorRepository,
        IDocumentoRepository documentoRepository,
        IItemRepository itemRepository,
        IItemVersaoRepository itemVersaoRepository,
        IMovimentacoesRepository movimentacoesRepository,
        IPagamentoRepository pagamentoRepository,
        IRessarcimentoRepository ressarcimentoRepository,
        IRegistroProcessamentoRepository registroProcessamentoRepository,
        IMapper mapper) : IMovimentacoesService
    {
        private readonly IAgregadorRepository _agregadorRepository = agregadorRepository;
        private readonly IDocumentoRepository _documentoRepository = documentoRepository;
        private readonly IItemRepository _itemRepository = itemRepository;
        private readonly IItemVersaoRepository _itemVersaoRepository = itemVersaoRepository;
        private readonly IMovimentacoesRepository _movimentacoesRepository = movimentacoesRepository;
        private readonly IPagamentoRepository _pagamentoRepository = pagamentoRepository;
        private readonly IRessarcimentoRepository _ressarcimentoRepository = ressarcimentoRepository;
        private readonly IRegistroProcessamentoRepository _registroProcessamentoRepository = registroProcessamentoRepository;
        private readonly IMapper _mapper = mapper;

        public async Task<ObterMovimentacaoDetalhesDto?> ObterDetalhesMovimentacaoAsync(int id)
        {
            var movimentacao = await _movimentacoesRepository.BuscarDetalhesMovimentacaoAsync(id);

            if (movimentacao == null || movimentacao.Agregador == null)
                return null;

            var detalhesDto = _mapper.Map<ObterMovimentacaoDetalhesDto>(movimentacao);

            if (movimentacao.Documento?.Emitente != null)
                detalhesDto.Fornecedor = _mapper.Map<FornecedorDto>(movimentacao.Documento.Emitente);

            var veiculo = movimentacao.Agregador?.Ativos?.OfType<Veiculo>().FirstOrDefault();
            if (veiculo != null)
            {
                detalhesDto.Ativo = _mapper.Map<AtivoDto>(veiculo);

                var oficina = veiculo.Oficinas?.FirstOrDefault()?.Oficina;
                if (oficina != null)
                    detalhesDto.Oficina = _mapper.Map<OficinaDto>(oficina);
            }

            var pagamentos = await _pagamentoRepository.BuscarPorAgregadorIdAsync(movimentacao.Agregador.Id);
            var ressarcimentos = await _ressarcimentoRepository.BuscarPorAgregadorIdAsync(movimentacao.Agregador.Id);

            detalhesDto.ValoresConsolidados = new ValoresConsolidadosDto
            {
                APagar = pagamentos.Where(p => p.StatusPagamento == StatusPagamento.Pendente || p.StatusPagamento == StatusPagamento.PagamentoAgendado || p.StatusPagamento == StatusPagamento.AguardandoConfirmacao).Sum(p => p.Valor),
                Pago = pagamentos.Where(p => p.StatusPagamento == StatusPagamento.Pago || p.StatusPagamento == StatusPagamento.PagoParcialmente).Sum(p => p.Valor),
                ARessarcir = ressarcimentos.Where(r => r.StatusRessarcimento == StatusRessarcimento.Pendente || r.StatusRessarcimento == StatusRessarcimento.Aprovado).Sum(r => r.Valor),
                Ressarcido = ressarcimentos.Where(r => r.StatusRessarcimento == StatusRessarcimento.Pago).Sum(r => r.Valor)
            };

            var documentos = await _documentoRepository.BuscarAsync(d => d.IdAgregador == movimentacao.Agregador.Id);
            if (documentos.Any())
            {
                detalhesDto.Documentos = _mapper.Map<List<DocumentoDto>>(documentos);
            }

            return detalhesDto;
        }

        public Task<MovimentacaoDetalhesDto> DetalheMovimentacao(int idMovimentacao)
        {
            throw new NotImplementedException();
        }

        public async Task<List<MovimentacaoDto>> ListarMovimentacoesAsync()
        {
            //var listaAgregadorMovimentacoesDto = new List<AgregadorMovimentacoesDto>();

            //var agregadores = await _agregadorRepository.ListarAsync();
            //foreach (var agregador in agregadores)
            //{
            //    // 1. Buscar todos os itens do agregador pelo Id
            //    var itens = await _itemRepository.BuscarAsync(i => i.IdAgregador == agregador.Id);

            //    // 2. Para cada item, buscar suas versões
            //    var itensVersoes = new List<ItemVersao>();
            //    foreach (var item in itens)
            //    {
            //        var versoes = await _itemVersaoRepository.BuscarPorItemIdAsync(item.Id);
            //        itensVersoes.AddRange(versoes);
            //    }

            //    //var documentos = await _documentoRepository.BuscarAsync(d => d.IdAgregador == agregador.Id);

            //    string? codAtivo = "Não encontrado";

            //    if (agregador.Ativos.FirstOrDefault()?.Tipo == TipoAtivo.Veiculo)
            //    {
            //        codAtivo = (agregador.Ativos.FirstOrDefault() as Veiculo)!.Placa;
            //    }

            //    var listaFornecedores = itensVersoes.Select(x => x.PessoaFornecedora);
            //    foreach (var fornecedor in listaFornecedores)
            //    {
            //        // Gerar uma lista de itensVersoes agrupada por DataHoraAutorizacao
            //        var itensVersoesAgrupadosPorDataAutorizacao = itensVersoes
            //            .Where(iv => iv.DataHoraAutorizacao.HasValue)
            //            .GroupBy(iv => iv.DataHoraAutorizacao!.Value.Date)
            //            .ToDictionary(
            //                g => g.Key,
            //                g => g.ToList()
            //            );

            //        var movimentacoes = itensVersoesAgrupadosPorDataAutorizacao.Select(grupo =>
            //        {

            //            return new MovimentacaoDto
            //            {
            //                ValorTotal = grupo.Value.Sum(iv => iv.ValorTotal),
            //                Situacao = 100, // TODO: Definir situação da movimentação

            //                Itens = [.. grupo.Value.Select(iv => new MovimentacaoItemDto
            //                {
            //                    Codigo = iv.Item!.Codigo,
            //                    Descricao = iv.Item.Descricao,
            //                    Quantidade = iv.Quantidade,
            //                    //Situacao = iv.Status, // TODO: Definir situação do item
            //                    ValorTotal = iv.ValorTotal,
            //                    ValorUnitario = iv.ValorUnitario,
            //                    DataHoraAutorizacao = iv.DataHoraAutorizacao ?? DateTime.MinValue
            //                })]
            //            };
            //        }
            //        ).ToList();

            //        var agregadorMovimentacoes = new AgregadorMovimentacoesDto
            //        {
            //            NumeroAgregador = agregador.Numero,
            //            TipoAgregador = agregador.TipoAgregador,
            //            CodigoAtivo = codAtivo,
            //            Empresa = agregador.ClienteEmpresa != null ?
            //                new MovimentacaoPessoaDto
            //                {
            //                    Cnpj = agregador.ClienteEmpresa.Cnpj,
            //                    NomeFantasia = agregador.ClienteEmpresa.NomeFantasia,
            //                    RazaoSocial = agregador.ClienteEmpresa.RazaoSocial,
            //                    TipoPessoa = agregador.ClienteEmpresa.TipoPessoa
            //                }
            //                : null,
            //            Situacao = 100, // TODO: Definir situação do agregador
            //            ValorTotalAgregado = movimentacoes.Sum(m => m.ValorTotal),
            //            Movimentacoes = movimentacoes
            //        };

            //        listaAgregadorMovimentacoesDto.Add(agregadorMovimentacoes);
            //    }
            //}

            //return new ListaMovimentacoesDto
            //{
            //    AgregadorMovimentacoes = listaAgregadorMovimentacoesDto
            //};

            throw new NotImplementedException("Método ainda não implementado.");
        }

        public async Task<List<ItemAgregadorDto>> ObterItensOutrasMovimentacoesAsync(int idAgregador, int movId)
        {
            var existemMovimentacoes = await _movimentacoesRepository
                .ExistemMovimentacoesPorAgregadorExcluindoMovimentacaoAsync(idAgregador, movId);

            if (!existemMovimentacoes)
                return [];

            var itensComVersoes = await _itemRepository
                .BuscarItensComVersoesPorAgregadorExcluindoMovimentacaoAsync(idAgregador, movId);

            var result = new List<ItemAgregadorDto>();

            foreach (var item in itensComVersoes)
            {
                var versoesValidas = item.Versoes
                    .Where(v => v.IdMovimentacao.HasValue && v.IdMovimentacao != movId).ToList();

                if (!versoesValidas.Any())
                    continue;

                var valorAtualizado = CalcularValorAtualizado(versoesValidas);

                var versaoIds = versoesValidas.Select(v => v.Id).ToList();

                var temDivergencias = await _registroProcessamentoRepository
                    .ExistemDivergenciasPorItensVersaoAsync(versaoIds);

                var versoesDto = new List<VersaoAgregadorDto>();

                foreach (var versao in versoesValidas)
                {
                    var nomeFornecedor = ObterNomeDoFornecedor(versao.PessoaFornecedora);

                    var documentos = ObterDocumentosDaVersao(versao.Documento);

                    var registrosProcessamento = await _registroProcessamentoRepository
                        .BuscarPorItensVersaoAsync(new[] { versao.Id });

                    var divergenciasVersao = ObterDivergencias(registrosProcessamento);

                    versoesDto.Add(new VersaoAgregadorDto
                    {
                        IdItemVersao = versao.Id,
                        Fornecedor = new FornecedorAgregadorDto { Nome = nomeFornecedor ?? string.Empty },
                        TipoMovimento = versao.TipoMovimento == TipoMovimentoItem.Autorizacao ? "Autorizacao" : "Exclusao",
                        DataAutorizacao = versao.DataHoraAutorizacao,
                        Documentos = documentos,
                        Quantidade = versao.Quantidade,
                        ValorTotal = versao.ValorTotal,
                        DataEntrega = versao.DataEntrega,
                        Divergencias = divergenciasVersao
                    });
                }

                result.Add(new ItemAgregadorDto
                {
                    IdItem = item.Id,
                    Codigo = item.Codigo,
                    Descricao = item.Descricao,
                    ValorAtualizado = valorAtualizado,
                    Divergencias = temDivergencias,
                    Versoes = versoesDto
                });
            }

            return result;
        }

        private decimal CalcularValorAtualizado(List<ItemVersao> versoesValidas)
        {
            var valorAutorizacoes = versoesValidas
                .Where(v => v.TipoMovimento == TipoMovimentoItem.Autorizacao)
                .Sum(v => v.ValorTotal);

            var valorExclusoes = versoesValidas
                .Where(v => v.TipoMovimento == TipoMovimentoItem.Exclusao)
                .Sum(v => v.ValorTotal);

            return (valorAutorizacoes - valorExclusoes);
        }

        private string ObterNomeDoFornecedor(Pessoa? pessoa)
        {
            var nomeFornecedor = pessoa switch
            {
                PessoaFisica pf => pf.Nome,
                PessoaJuridica pj => pj.RazaoSocial,
                _ => string.Empty
            };

            return nomeFornecedor;
        }

        private List<DocumentoAgregadorDto> ObterDocumentosDaVersao(Documento? documento)
        {
            //TODO: Hoje a classe ItemVersao mapeia apenas um documento e não uma array (public Documento? Documento { get; set; })
            var documentos = new List<DocumentoAgregadorDto>();

            if (documento != null)
            {
                documentos.Add(new DocumentoAgregadorDto
                {
                    TipoDocumento = documento.TipoDocumento,
                    Numero = documento.Numero ?? string.Empty
                });
            }

            return documentos;
        }

        private List<DivergenciaAgregadorDto> ObterDivergencias(IEnumerable<RegistroProcessamentoItemVersao> registrosProcessamento)
        {
            var divergenciasVersao = registrosProcessamento
                        .Where(r => r.Divergencia.HasValue)
                        .Select(r => new DivergenciaAgregadorDto
                        {
                            Tipo = r.Divergencia!.Value.ToString(),
                            Descricao = r.Divergencia!.Value.ToString(),
                            Detalhe = r.Detalhe
                        })
                        .ToList();

            return divergenciasVersao;
        }
    }
}
