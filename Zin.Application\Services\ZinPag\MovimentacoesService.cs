using AutoMapper;
using Microsoft.Extensions.Configuration;
using Zin.Application.DTOs.Documentos;
using Zin.Application.DTOs.Movimentacoes;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;
using Microsoft.EntityFrameworkCore;
using Zin.Infrastructure.Dados;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Application.Services.ZinPag
{
    public class MovimentacoesService(
        IAgregadorRepository agregadorRepository,
        IDocumentoRepository documentoRepository,
        IItemRepository itemRepository,
        IItemVersaoRepository itemVersaoRepository,
        IMovimentacoesRepository movimentacoesRepository,
        IPagamentoRepository pagamentoRepository,
        IRessarcimentoRepository ressarcimentoRepository,
        IConfiguration configuration,
        I<PERSON><PERSON>per mapper,
        ZinDbContext context) : IMovimentacoesService
    {
        private readonly IAgregadorRepository _agregadorRepository = agregadorRepository;
        private readonly IDocumentoRepository _documentoRepository = documentoRepository;
        private readonly IItemRepository _itemRepository = itemRepository;
        private readonly IItemVersaoRepository _itemVersaoRepository = itemVersaoRepository;
        private readonly IMovimentacoesRepository _movimentacoesRepository = movimentacoesRepository;
        private readonly IPagamentoRepository _pagamentoRepository = pagamentoRepository;
        private readonly IRessarcimentoRepository _ressarcimentoRepository = ressarcimentoRepository;
        private readonly IConfiguration _configuration = configuration;
        private readonly IMapper _mapper = mapper;
        private readonly ZinDbContext _context = context;

        public async Task<ObterMovimentacaoDetalhesDto?> ObterDetalhesMovimentacaoAsync(int id)
        {
            var movimentacao = await _movimentacoesRepository.BuscarDetalhesMovimentacaoAsync(id);

            if (movimentacao == null || movimentacao.Agregador == null)
                return null;

            var detalhesDto = _mapper.Map<ObterMovimentacaoDetalhesDto>(movimentacao);

            if (movimentacao.Documento?.Emitente != null)
                detalhesDto.Fornecedor = _mapper.Map<FornecedorDto>(movimentacao.Documento.Emitente);

            var veiculo = movimentacao.Agregador?.Ativos?.OfType<Veiculo>().FirstOrDefault();
            if (veiculo != null)
            {
                detalhesDto.Ativo = _mapper.Map<AtivoDto>(veiculo);

                var oficina = veiculo.Oficinas?.FirstOrDefault()?.Oficina;
                if (oficina != null)
                    detalhesDto.Oficina = _mapper.Map<OficinaDto>(oficina);
            }

            var pagamentos = await _pagamentoRepository.BuscarPorAgregadorIdAsync(movimentacao.Agregador.Id);
            var ressarcimentos = await _ressarcimentoRepository.BuscarPorAgregadorIdAsync(movimentacao.Agregador.Id);

            detalhesDto.ValoresConsolidados = new ValoresConsolidadosDto
            {
                APagar = pagamentos.Where(p => p.StatusPagamento == StatusPagamento.Pendente || p.StatusPagamento == StatusPagamento.PagamentoAgendado || p.StatusPagamento == StatusPagamento.AguardandoConfirmacao).Sum(p => p.Valor),
                Pago = pagamentos.Where(p => p.StatusPagamento == StatusPagamento.Pago || p.StatusPagamento == StatusPagamento.PagoParcialmente).Sum(p => p.Valor),
                ARessarcir = ressarcimentos.Where(r => r.StatusRessarcimento == StatusRessarcimento.Pendente || r.StatusRessarcimento == StatusRessarcimento.Aprovado).Sum(r => r.Valor),
                Ressarcido = ressarcimentos.Where(r => r.StatusRessarcimento == StatusRessarcimento.Pago).Sum(r => r.Valor)
            };

            var documentos = await _documentoRepository.BuscarAsync(d => d.IdAgregador == movimentacao.Agregador.Id);
            if (documentos.Any())
            {
                detalhesDto.Documentos = _mapper.Map<List<DocumentoDto>>(documentos);
            }

            return detalhesDto;
        }

        public Task<MovimentacaoDetalhesDto> DetalheMovimentacao(int idMovimentacao)
        {
            throw new NotImplementedException();
        }

        public async Task<List<MovimentacaoDto>> ListarMovimentacoesAsync()
        {
            //var listaAgregadorMovimentacoesDto = new List<AgregadorMovimentacoesDto>();

            //var agregadores = await _agregadorRepository.ListarAsync();
            //foreach (var agregador in agregadores)
            //{
            //    // 1. Buscar todos os itens do agregador pelo Id
            //    var itens = await _itemRepository.BuscarAsync(i => i.IdAgregador == agregador.Id);

            //    // 2. Para cada item, buscar suas versões
            //    var itensVersoes = new List<ItemVersao>();
            //    foreach (var item in itens)
            //    {
            //        var versoes = await _itemVersaoRepository.BuscarPorItemIdAsync(item.Id);
            //        itensVersoes.AddRange(versoes);
            //    }

            //    //var documentos = await _documentoRepository.BuscarAsync(d => d.IdAgregador == agregador.Id);

            //    string? codAtivo = "Não encontrado";

            //    if (agregador.Ativos.FirstOrDefault()?.Tipo == TipoAtivo.Veiculo)
            //    {
            //        codAtivo = (agregador.Ativos.FirstOrDefault() as Veiculo)!.Placa;
            //    }

            //    var listaFornecedores = itensVersoes.Select(x => x.PessoaFornecedora);
            //    foreach (var fornecedor in listaFornecedores)
            //    {
            //        // Gerar uma lista de itensVersoes agrupada por DataHoraAutorizacao
            //        var itensVersoesAgrupadosPorDataAutorizacao = itensVersoes
            //            .Where(iv => iv.DataHoraAutorizacao.HasValue)
            //            .GroupBy(iv => iv.DataHoraAutorizacao!.Value.Date)
            //            .ToDictionary(
            //                g => g.Key,
            //                g => g.ToList()
            //            );

            //        var movimentacoes = itensVersoesAgrupadosPorDataAutorizacao.Select(grupo =>
            //        {

            //            return new MovimentacaoDto
            //            {
            //                ValorTotal = grupo.Value.Sum(iv => iv.ValorTotal),
            //                Situacao = 100, // TODO: Definir situação da movimentação

            //                Itens = [.. grupo.Value.Select(iv => new MovimentacaoItemDto
            //                {
            //                    Codigo = iv.Item!.Codigo,
            //                    Descricao = iv.Item.Descricao,
            //                    Quantidade = iv.Quantidade,
            //                    //Situacao = iv.Status, // TODO: Definir situação do item
            //                    ValorTotal = iv.ValorTotal,
            //                    ValorUnitario = iv.ValorUnitario,
            //                    DataHoraAutorizacao = iv.DataHoraAutorizacao ?? DateTime.MinValue
            //                })]
            //            };
            //        }
            //        ).ToList();

            //        var agregadorMovimentacoes = new AgregadorMovimentacoesDto
            //        {
            //            NumeroAgregador = agregador.Numero,
            //            TipoAgregador = agregador.TipoAgregador,
            //            CodigoAtivo = codAtivo,
            //            Empresa = agregador.ClienteEmpresa != null ?
            //                new MovimentacaoPessoaDto
            //                {
            //                    Cnpj = agregador.ClienteEmpresa.Cnpj,
            //                    NomeFantasia = agregador.ClienteEmpresa.NomeFantasia,
            //                    RazaoSocial = agregador.ClienteEmpresa.RazaoSocial,
            //                    TipoPessoa = agregador.ClienteEmpresa.TipoPessoa
            //                }
            //                : null,
            //            Situacao = 100, // TODO: Definir situação do agregador
            //            ValorTotalAgregado = movimentacoes.Sum(m => m.ValorTotal),
            //            Movimentacoes = movimentacoes
            //        };

            //        listaAgregadorMovimentacoesDto.Add(agregadorMovimentacoes);
            //    }
            //}

            //return new ListaMovimentacoesDto
            //{
            //    AgregadorMovimentacoes = listaAgregadorMovimentacoesDto
            //};

            throw new NotImplementedException("Método ainda não implementado.");
        }

        public async Task<List<ItemAgregadorDto>> ObterItensOutrasMovimentacoesAsync(int idAgregador, int movId)
        {
            // First, find all movements in the same aggregator excluding the current movement
            var movimentacoesDoAgregador = await _context.Movimentacoes
                .Where(m => m.IdAgregador == idAgregador && m.IdMovimentacao != movId)
                .Select(m => m.IdAgregador)
                .ToListAsync();

            if (!movimentacoesDoAgregador.Any())
                return new List<ItemAgregadorDto>();

            // Get all items from the aggregator with their versions, including all necessary joins
            var itensComVersoes = await _context.Itens
                .Where(i => i.IdAgregador == idAgregador)
                .Include(i => i.Versoes.Where(v => v.IdMovimentacao.HasValue && v.IdMovimentacao != movId))
                    .ThenInclude(v => v.PessoaFornecedora)
                .Include(i => i.Versoes.Where(v => v.IdMovimentacao.HasValue && v.IdMovimentacao != movId))
                    .ThenInclude(v => v.Documento)
                .ToListAsync();

            var result = new List<ItemAgregadorDto>();

            foreach (var item in itensComVersoes)
            {
                var versoesValidas = item.Versoes.Where(v => v.IdMovimentacao.HasValue && v.IdMovimentacao != movId).ToList();

                if (!versoesValidas.Any())
                    continue;

                // Calculate valorAtualizado: sum of "autorizações" minus sum of "exclusões"
                var valorAutorizacoes = versoesValidas
                    .Where(v => v.TipoMovimento == TipoMovimentoItem.Autorizacao)
                    .Sum(v => v.ValorTotal);

                var valorExclusoes = versoesValidas
                    .Where(v => v.TipoMovimento == TipoMovimentoItem.Exclusao)
                    .Sum(v => v.ValorTotal);

                var valorAtualizado = valorAutorizacoes - valorExclusoes;

                // Check if any version has divergences
                var versaoIds = versoesValidas.Select(v => v.Id).ToList();
                var temDivergencias = await _context.RegistroProcessamento
                    .AnyAsync(r => versaoIds.Contains(r.IdItemVersao) && r.Divergencia.HasValue);

                var versoesDto = new List<VersaoAgregadorDto>();

                foreach (var versao in versoesValidas)
                {
                    // Get supplier name
                    var fornecedorNome = versao.PessoaFornecedora switch
                    {
                        PessoaFisica pf => pf.Nome,
                        PessoaJuridica pj => pj.RazaoSocial,
                        _ => string.Empty
                    };

                    // Get documents for this version
                    var documentos = new List<DocumentoAgregadorDto>();
                    if (versao.Documento != null)
                    {
                        documentos.Add(new DocumentoAgregadorDto
                        {
                            TipoDocumento = versao.Documento.TipoDocumento,
                            Numero = versao.Documento.Numero ?? string.Empty
                        });
                    }

                    // Get divergences for this version
                    var divergenciasVersao = await _context.RegistroProcessamento
                        .Where(r => r.IdItemVersao == versao.Id && r.Divergencia.HasValue)
                        .Select(r => new DivergenciaAgregadorDto
                        {
                            Tipo = r.Divergencia!.Value.ToString(),
                            Descricao = r.Divergencia!.Value.ToString(),
                            Detalhe = r.Detalhe
                        })
                        .ToListAsync();

                    versoesDto.Add(new VersaoAgregadorDto
                    {
                        IdItemVersao = versao.Id,
                        Fornecedor = new FornecedorAgregadorDto { Nome = fornecedorNome ?? string.Empty },
                        TipoMovimento = versao.TipoMovimento == TipoMovimentoItem.Autorizacao ? "Autorizacao" : "Exclusao",
                        DataAutorizacao = versao.DataHoraAutorizacao,
                        Documentos = documentos,
                        Quantidade = versao.Quantidade,
                        ValorTotal = versao.ValorTotal,
                        DataEntrega = versao.DataEntrega,
                        Divergencias = divergenciasVersao
                    });
                }

                result.Add(new ItemAgregadorDto
                {
                    IdItem = item.Id,
                    Codigo = item.Codigo,
                    Descricao = item.Descricao,
                    ValorAtualizado = valorAtualizado,
                    Divergencias = temDivergencias,
                    Versoes = versoesDto
                });
            }

            return result;
        }
    }
}
