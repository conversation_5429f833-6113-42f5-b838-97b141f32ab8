﻿using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class ItemRepository(IUnitOfWork unitOfWork) : IItemRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task<Item?> BuscarPorIdAsync(int id)
        {
            return await Context.Itens.Include(i => i.Versoes).FirstOrDefaultAsync(i => i.Id == id)
                ?? throw new EntidadeNaoEncontradaExcecao($"Item com ID {id} não encontrado.");
        }
        public async Task<Item?> BuscarPorCodigoAsync(string codigo)
        {
            return await Context.Itens.Include(i => i.Versoes).FirstOrDefaultAsync(i => i.Codigo == codigo);
        }

        public async Task<IEnumerable<Item>> ListarAsync()
        {
            return await Context.Itens.Include(i => i.Versoes).ToListAsync();
        }

        public async Task<IEnumerable<Item>> BuscarAsync(Expression<Func<Item, bool>> predicado)
        {
            return await Context.Itens.Where(predicado).ToListAsync();
        }

        public async Task<int> InserirAsync(Item entidade)
        {
            await Context.Itens.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public async Task AtualizarAsync(Item entidade)
        {
            Context.Itens.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task DeletarAsync(int id)
        {
            var entidade = await BuscarPorIdAsync(id);

            if (entidade == null)
                throw new EntidadeNaoEncontradaExcecao($"Item com ID {id} não encontrado.");

            Context.Itens.Remove(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<Item>> BuscarPorAgregadorIdAsync(int agregadorId)
        {
            return await Context.Itens
                .Where(item => item.IdAgregador == agregadorId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Item>> BuscarItensComVersoesPorAgregadorExcluindoMovimentacaoAsync(int idAgregador, int movIdExcluir)
        {
            return await Context.Itens
                .Where(i => i.IdAgregador == idAgregador)
                .Include(i => i.Versoes.Where(v => v.IdMovimentacao.HasValue && v.IdMovimentacao != movIdExcluir))
                    .ThenInclude(v => v.PessoaFornecedora)
                .Include(i => i.Versoes.Where(v => v.IdMovimentacao.HasValue && v.IdMovimentacao != movIdExcluir))
                    .ThenInclude(v => v.Documento)
                .ToListAsync();
        }

    }
}
