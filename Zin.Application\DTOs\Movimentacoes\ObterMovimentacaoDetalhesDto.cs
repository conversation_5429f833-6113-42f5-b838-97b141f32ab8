using Zin.Application.DTOs.Documentos;

namespace Zin.Application.DTOs.Movimentacoes
{
    public class ObterMovimentacaoDetalhesDto
    {
        public int IdAgregador { get; set; }
        public int IdMovimentacao { get; set; }
        public string? NumeroSinistro { get; set; }
        public FornecedorDto? Fornecedor { get; set; }
        public OficinaDto? Oficina { get; set; }
        public AtivoDto? Ativo { get; set; }
        public ValoresConsolidadosDto? ValoresConsolidados { get; set; }
        public List<DocumentoDto> Documentos { get; set; }
    }

    public class ValoresConsolidadosDto
    {
        public decimal APagar { get; set; }
        public decimal Pago { get; set; }
        public decimal ARessarcir { get; set; }
        public decimal Ressarcido { get; set; }
    }
}