﻿using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;
using Zin.Domain.Repositorios;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class DocumentoRepository(IUnitOfWork unitOfWork) : IDocumentoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;

        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(Documento entidade)
        {
            Context.Documentos.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<Documento>> BuscarAsync(Expression<Func<Documento, bool>> predicado)
        {
            return await Context.Documentos.Where(predicado).ToListAsync();
        }

        public async Task<Documento> ObterPorIdAsync(int id)
        {
            return await Context.Documentos.FindAsync(id)
                .AsTask() ?? throw new Exception($"Documento com ID {id} não encontrado.");
        }

        Task<Documento> IRepositoryBase<Documento, int>.BuscarPorIdAsync(int id)
        {
            return ObterPorIdAsync(id);
        }

        public Task<Documento?> BuscarPorNumero(string numero)
        {
            return Context.Documentos.FirstOrDefaultAsync(d => d.Numero == numero);
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<Documento> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<int> InserirAsync(Documento entidade)
        {
            await Context.Documentos.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id; 
        }

        public async Task<int[]> InserirVariosAsync(IEnumerable<Documento> entidades)
        {
            if (entidades is null)
                throw new ArgumentNullException(nameof(entidades));

            var documentos = entidades.ToList();
            await Context.Documentos.AddRangeAsync(documentos);
            await _unitOfWork.CommitAsync();
            return documentos.Select(d => d.Id).ToArray();
        }

        public Task<IEnumerable<Documento>> ListarAsync()
        {
            throw new NotImplementedException();
        }
    }
}
