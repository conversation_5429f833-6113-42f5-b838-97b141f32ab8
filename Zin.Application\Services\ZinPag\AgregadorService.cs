using AutoMapper;
using Zin.Application.DTOs.Agregadores;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class AgregadorService(
        IAgregadorRepository agregadorRepositorio,
        IMapper mapper) : IAgregadorService
    {
        private readonly IAgregadorRepository _agregadorRepositorio = agregadorRepositorio;
        private readonly IMapper _mapper = mapper;

        public async Task<IEnumerable<AgregadorDto>> ListarAgregadoresAsync()
        {
            var agregadores = await _agregadorRepositorio.ListarAsync();
            return _mapper.Map<IEnumerable<AgregadorDto>>(agregadores);
        }

        public async Task<AgregadorDto?> ObterAgregadorPorIdAsync(int id)
        {
            var agregador = await _agregadorRepositorio.BuscarPorIdAsync(id);
            return _mapper.Map<AgregadorDto>(agregador);
        }

        public async Task<AgregadorDto> CriarAgregadorAsync(CriaAgregadorDto dto)
        {

            var agregador = _mapper.Map<Agregador>(dto);

            var idGerado = await _agregadorRepositorio.InserirAsync(agregador);
            var criado = await _agregadorRepositorio.BuscarPorIdAsync(idGerado);

            return _mapper.Map<AgregadorDto>(criado);
        }
        public async Task AtualizarAgregadorAsync(int id, AtualizaAgregadorDto dto)
        {
            var agregadorExistente = await _agregadorRepositorio.BuscarPorIdAsync(id);

            if (agregadorExistente == null)
                throw new Exception($"Agregador com ID {id} não encontrado.");

            _mapper.Map(dto, agregadorExistente);

            await _agregadorRepositorio.AtualizarAsync(agregadorExistente);
        }

        public async Task RemoverAgregadorAsync(int id)
        {
            await _agregadorRepositorio.DeletarAsync(id);
        }

        
    }
}