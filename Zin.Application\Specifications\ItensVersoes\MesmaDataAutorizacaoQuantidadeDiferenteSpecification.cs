﻿using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Application.Helpers;
using Zin.Application.Specifications.ItensVersoes.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;

namespace Zin.Application.Specifications.ItensVersoes
{
    /// <summary>
    /// Specification para verificar se a quantidade de um item recebido é a diferente
    /// da última versão com a mesma autorização e fornecedor
    /// devendo ser criada uma nova versão do item.
    /// </summary>
    public class MesmaDataAutorizacaoQuantidadeDiferenteSpecification : IItemVersaoSpecification
    {
        public DeveCriarNovaVersaoResponse IsSatisfiedBy(ICollection<ItemVersao> todasVersoes, ImportarItensDTO itemDto)
        {
            bool deveCriarNovaVersao = false;
            ItemVersao? versaoAnterior = null;

            // Verifica se o tipo de movimento é autorização, pois essa regra só se aplica a esse tipo
            if (itemDto.TipoMovimentoItem == TipoMovimentoItem.Autorizacao)
            {
                var ultimaVersaoMesmaAutorizacao = ItemVersaoSpecificationHelper.ObterUltimaVersaoMesmaAutorizacao(
                    todasVersoes,
                    itemDto.DataAutorizacao,
                    itemDto.CnpjFornecedor);

                if (ultimaVersaoMesmaAutorizacao != null)
                {
                    // A data de autorização é a mesma da última versão com a mesma autorização e fornecedor,
                    // mas a quantidade é diferente, então deve ser criada uma nova versão
                    deveCriarNovaVersao = ultimaVersaoMesmaAutorizacao.Quantidade != itemDto.Quantidade;

                    if (deveCriarNovaVersao)
                    {
                        versaoAnterior = ultimaVersaoMesmaAutorizacao;
                    }
                }
            }

            return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(deveCriarNovaVersao, versaoAnterior);
        }
    }
}
