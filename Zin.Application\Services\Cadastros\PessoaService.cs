﻿using AutoMapper;
using Zin.Application.DTOs.Pessoas;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros
{
    public class PessoaService(
        IMapper mapper,
        IPessoaRepository pessoaRepository) : IPessoaService
    {
        private readonly IMapper _mapper = mapper;
        private readonly IPessoaRepository _pessoaRepository = pessoaRepository;

        public async Task AtualizarPessoaAsync(int id, AtualizaPessoaDto pessoaDto)
        {
            if (pessoaDto == null)
            {
                throw new ArgumentNullException(nameof(pessoaDto), "Pessoa não pode ser nula.");
            }

            // Busca a pessoa existente
            var pessoaExistente = await _pessoaRepository.BuscarPorIdAsync(id);
            if (pessoaExistente == null)
            {
                throw new InvalidOperationException($"Pessoa com id {id} não encontrada.");
            }

            // Atualiza apenas os campos populados no DTO
            switch (pessoaDto.TipoPessoa)
            {
                case TipoPessoa.Fisica when pessoaExistente is PessoaFisica pf:
                    if (!string.IsNullOrWhiteSpace(pessoaDto.Nome))
                        pf.Nome = pessoaDto.Nome;
                    if (!string.IsNullOrWhiteSpace(pessoaDto.Sobrenome))
                        pf.Sobrenome = pessoaDto.Sobrenome;
                    if (!string.IsNullOrWhiteSpace(pessoaDto.Cpf))
                        pf.Cpf = pessoaDto.Cpf;
                    break;

                case TipoPessoa.Juridica when pessoaExistente is PessoaJuridica pj:
                    if (!string.IsNullOrWhiteSpace(pessoaDto.RazaoSocial))
                        pj.RazaoSocial = pessoaDto.RazaoSocial;
                    if (!string.IsNullOrWhiteSpace(pessoaDto.NomeFantasia))
                        pj.NomeFantasia = pessoaDto.NomeFantasia;
                    if (!string.IsNullOrWhiteSpace(pessoaDto.Cnpj))
                        pj.Cnpj = pessoaDto.Cnpj;
                    break;

                default:
                    throw new ArgumentException("Tipo de pessoa inválido ou incompatível com a entidade existente.", nameof(pessoaDto));
            }

            await _pessoaRepository.AtualizarAsync(pessoaExistente);
        }

        public async Task<PessoaDto?> BuscarPessoaPorIdAsync(int id)
        {
            var pessoa = await _pessoaRepository.BuscarPorIdAsync(id);
            if (pessoa is PessoaFisica pf)
            {
                return new PessoaDto
                {
                    Id = pf.Id,
                    TipoPessoa = pf.TipoPessoa,
                    Nome = pf.Nome,
                    Sobrenome = pf.Sobrenome,
                    Cpf = pf.Cpf
                };
            }
            if (pessoa is PessoaJuridica pj)
            {
                return new PessoaDto
                {
                    Id = pj.Id,
                    TipoPessoa = pj.TipoPessoa,
                    RazaoSocial = pj.RazaoSocial,
                    NomeFantasia = pj.NomeFantasia,
                    Cnpj = pj.Cnpj
                };
            }
            if (pessoa != null)
            {
                return new PessoaDto
                {
                    Id = pessoa.Id,
                    TipoPessoa = pessoa.TipoPessoa
                };
            }
            return null;
        }

        public async Task<int> CriaPessoaAsync(CriaPessoaDto dto)
        {
            Pessoa pessoa;
            if (dto.TipoPessoa == TipoPessoa.Fisica)
            {
                pessoa = new PessoaFisica
                {
                    TipoPessoa = dto.TipoPessoa,
                    Nome = dto.Nome!,
                    Sobrenome = dto.Sobrenome!,
                    Cpf = dto.Cpf!
                };
            }
            else if (dto.TipoPessoa == TipoPessoa.Juridica)
            {
                pessoa = new PessoaJuridica
                {
                    TipoPessoa = dto.TipoPessoa,
                    RazaoSocial = dto.RazaoSocial!,
                    NomeFantasia = dto.NomeFantasia!,
                    Cnpj = dto.Cnpj!
                };
            }
            else
            {
                throw new ArgumentException("Tipo de pessoa inválido.", nameof(dto));
            }

            return await _pessoaRepository.InserirAsync(pessoa);
        }

        public async Task DeletarPessoaAsync(int id)
        {
            await _pessoaRepository.DeletarAsync(id);
        }

        public async Task<IEnumerable<PessoaDto>> ListarPessoasAsync()
        {
            var pessoas = await _pessoaRepository.ListarAsync();

            return pessoas.Select(p =>
                p switch
                {
                    PessoaFisica pf => new PessoaDto
                    {
                        Id = pf.Id,
                        TipoPessoa = pf.TipoPessoa,
                        Nome = pf.Nome,
                        Sobrenome = pf.Sobrenome,
                        Cpf = pf.Cpf
                    },
                    PessoaJuridica pj => new PessoaDto
                    {
                        Id = pj.Id,
                        TipoPessoa = pj.TipoPessoa,
                        RazaoSocial = pj.RazaoSocial,
                        NomeFantasia = pj.NomeFantasia,
                        Cnpj = pj.Cnpj
                    },
                    _ => new PessoaDto
                    {
                        Id = p.Id,
                        TipoPessoa = p.TipoPessoa
                    }
                }
            );
        }
    }
}