﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Domain.Entidades.ZinPag.Movimentacoes
{
    [Table("movimentacoes", Schema = "zinpag")]
    public class Movimentacao
    {
        // 1. Chave primária
        [Key]
        [Column("id_movimentacao")]
        public int IdMovimentacao { get; set; }

        // 2. <PERSON><PERSON> estrangeira<PERSON>
        [Column("id_agregador")]
        public int IdAgregador { get; set; }

        [Column("id_documento")]
        public int? IdDocumento { get; set; }

        // 3. Propriedades simples
        [Column("data_hora_autorizacao")]
        public DateTime? DataHoraAutorizacao { get; set; }

        //[Column("data_hora_movimento")]
        //public DateTime? DataHoraMovimento { get; set; }


        // 4. Propriedades de navegação
        [ForeignKey(nameof(IdAgregador))]
        public Agregador? Agregador { get; set; }

        [ForeignKey(nameof(IdDocumento))]
        public Documento? Documento { get; set; }

        public List<ItemVersao> ItensVersoes { get; set; } = [];
    }
}
