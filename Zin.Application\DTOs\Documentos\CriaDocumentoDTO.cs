﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Application.DTOs.Itens;
using Zin.Application.DTOs.Pessoas;
using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Documentos
{
    public class CriaDocumentoDTO
    {
        // Campos comuns
        public TipoDocumento TipoDocumento { get; set; }
       
        // Campos de NotaFiscal
        public required string NumeroAgregador { get; set; } 
        public required CriaPessoaDto PessoaEmitente { get; set; }
        //Foi necessário incluír o IdPagamento como não obrigatório, para que o documento possa ser vinculado ao pagamento.
        public int IdPagamento { get; set; }
        public required string CnpjPessoaDestinatario { get; set; }
        public required string Numero { get; set; }
        public DateTime DataEmissao { get; set; }
        public decimal ValorTotal { get; set; }
        public string? Serie { get; set; }
        public decimal? ValorICMS { get; set; }
        public decimal? ValorIPI { get; set; }

        public List<CriarItemDocumentoDto> Itens { get; set; } = [];

        public IFormFile? XmlFile { get; set; }
    }
}
