﻿using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Importacao
{
    public class CriaImportacaoAgregadorDTO
    {
        public TipoAgregador TipoAgregador { get; set; }
        public required string Numero { get; set; }
        public string IdCliente { get; set; }

        public ImportarClienteEmpresaDTO ClienteEmpresa { get; set; }

        public IList<ImportarAtivosDTO> Ativos { get; set; }
        public IList<ImportarFornecedorDTO> Fornecedores { get; set; }
        public IList<ImportarOficinaDTO> Oficinas { get; set; }
        public IList<ImportarItensDTO> Itens { get; set; }
        public IList<ImportarDocumentoDTO> Documentos { get; set; }
    }
}
