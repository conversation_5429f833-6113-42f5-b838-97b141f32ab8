﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.Cadastros.Contatos
{
    [Table("contatos", Schema = "cadastros")]
    public class Contato
    {
        [Key]
        [Column("id_contato")]
        public int Id { get; set; }

        [Column("tipo_contato")]
        public TipoContato TipoContato { get; set; }

        [Column("nome")]
        public required string Nome { get; set; }

        [Column("valor")]
        public required string Valor { get; set; }

        [Column("observacao")]
        public string? Observacao { get; set; }
    }

}
