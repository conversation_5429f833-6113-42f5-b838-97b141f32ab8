﻿using Zin.Domain.Entidades.ZinPag.Ressarcimentos;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface IRessarcimentoRepository : IRepositoryBase<Ressarcimento, int>
    {
        Task<IEnumerable<Ressarcimento>> BuscarPorItemVersaoIdAsync(int itemVersaoId);
        Task<bool> ExisteRessarcimentoParaItemVersaoAsync(int itemVersaoId);
        Task<IEnumerable<Ressarcimento>> BuscarPorAgregadorIdAsync(int agregadorId);
    }
}
