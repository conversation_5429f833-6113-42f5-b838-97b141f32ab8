﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Zin.Domain.Entidades.Cadastros.Pessoas
{
    [Table("pessoas_juridicas", Schema = "cadastros")]
    public class PessoaJuridica : Pessoa
    {
        [Column("razao_social")]
        [StringLength(150)]
        public required string RazaoSocial { get; set; }

        [Column("nome_fantasia")]
        [StringLength(100)]
        public required string NomeFantasia { get; set; }

        [Column("cnpj")]
        [StringLength(14, MinimumLength = 14)]
        public required string Cnpj { get; set; }
    }
}