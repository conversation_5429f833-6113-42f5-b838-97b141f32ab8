﻿using System;
using System.Threading.Tasks;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.Processos
{
    public class RegistroProcessamentoService : IRegistroProcessamentoService
    {
        private readonly IRegistroProcessamentoRepository _registroProcessamentoRepositorio;
        private readonly IItemVersaoRepository _itemVersaoRepositorio;

        public RegistroProcessamentoService(
            IRegistroProcessamentoRepository registroProcessamentoRepositorio,
            IItemVersaoRepository itemVersaoRepositorio)
        {
            _registroProcessamentoRepositorio = registroProcessamentoRepositorio;
            _itemVersaoRepositorio = itemVersaoRepositorio;
        }

        public async Task TomarDecisaoDivergenciaPorItensVersaoAsync(
            IEnumerable<int> itensVersaoIds,
            DecisaoDivergencia decisao,
            string usuario)
        {
            var registros = await _registroProcessamentoRepositorio.BuscarPorItensVersaoAsync(itensVersaoIds);

            foreach (var registro in registros)
            {
                registro.Decisao = decisao;
                registro.Usuario = usuario;
                registro.DataAtualizacao = DateTime.UtcNow;
                await _registroProcessamentoRepositorio.AtualizarAsync(registro);

                // Atualize também o status da duplicidade no ItemVersao
                var itemVersao = await _itemVersaoRepositorio.BuscarPorIdAsync(registro.IdItemVersao);
                if (itemVersao == null)
                {
                    // Apenas continue para o próximo registro se não encontrar o ItemVersao
                    continue;
                }
                if (decisao == DecisaoDivergencia.ConfirmarDivergencia)
                {
                    itemVersao.StatusProcessamentoPagamentoDuplicado = StatusProcessamento.Duplicado;
                }
                else if (decisao == DecisaoDivergencia.CancelarDivergencia)
                {
                    itemVersao.StatusProcessamentoPagamentoDuplicado = StatusProcessamento.Processado;
                }
                await _itemVersaoRepositorio.AtualizarAsync(itemVersao);
            }
        }
    }
}