using Microsoft.EntityFrameworkCore;
using Zin.Domain.Entidades.Cadastros.Condicoes;
using Zin.Domain.Entidades.Cadastros.Contatos;
using Zin.Domain.Entidades.Cadastros.DadosBancarios;
using Zin.Domain.Entidades.Cadastros.Enderecos;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;

namespace Zin.Infrastructure.Dados
{
    public class ZinDbContext(DbContextOptions<ZinDbContext> options) : DbContext(options)
    {
        public DbSet<Pessoa> Pessoas { get; set; }
        public DbSet<PessoaFisica> PessoasFisicas { get; set; }
        public DbSet<PessoaJuridica> PessoasJuridicas { get; set; }
        public DbSet<ClienteEmpresa> ClienteEmpresas { get; set; }

        public DbSet<Contato> Contatos { get; set; }
        public DbSet<DadoBancario> DadosBancarios { get; set; }
        public DbSet<Endereco> Enderecos { get; set; }
        public DbSet<Estado> Estados { get; set; }
        public DbSet<Cidade> Cidades { get; set; }

        public DbSet<PessoaContato> PessoasContatos { get; set; }
        public DbSet<PessoaDadoBancario> PessoasDadosBancarios { get; set; }
        public DbSet<PessoaEndereco> PessoasEnderecos { get; set; }

        public DbSet<Agregador> Agregadores { get; set; }

        public DbSet<Ativo> Ativos { get; set; }
        public DbSet<Veiculo> Veiculos { get; set; }
        public DbSet<VeiculoOficina> VeiculoOficinas { get; set; }

        public DbSet<Pagamento> Pagamentos { get; set; }
        public DbSet<PagamentoItemVersao> PagamentoItemVersoes { get; set; }
        public DbSet<LiquidacaoPagamento> LiquidacoesPagamentos { get; set; }

        public DbSet<Item> Itens { get; set; }
        public DbSet<ItemVersao> ItensVersoes { get; set; }

        public DbSet<Documento> Documentos { get; set; }
        public DbSet<NotaFiscal> NotasFiscais { get; set; }
        public DbSet<DocumentoPagamento> DocumentoPagamentos { get; set; }
        public DbSet<DocumentoItemVersao> NotasFiscalItemVesrao { get; set; }

        public DbSet<Ressarcimento> Ressarcimentos { get; set; }
        public DbSet<RessarcimentoItemVersao> RessarcimentosItemVersoes { get; set; }

        public DbSet<ProcessamentoAgregadorStatus> ProcessamentoStatus { get; set; }
        public DbSet<RegistroProcessamentoItemVersao> RegistroProcessamento { get; set; }

        public DbSet<Movimentacao> Movimentacoes { get; set; } 

        public DbSet<Configuracao> Configuracoes { get; set; }
        public DbSet<Regra> Regras { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.HasDefaultSchema("public");

            // Relacionamentos muitos-para-muitos sem entidade de junção explícita
            modelBuilder.Entity<Agregador>()
                .HasMany(a => a.Ativos)
                .WithMany(b => b.Agregadores)
                .UsingEntity<Dictionary<string, object>>(
                    "agregadores_ativos",
                    j => j
                        .HasOne<Ativo>()
                        .WithMany()
                        .HasForeignKey("id_ativo")
                        .HasConstraintName("FK_agregadores_ativos_ativos_id_ativo"),
                    j => j
                        .HasOne<Agregador>()
                        .WithMany()
                        .HasForeignKey("id_agregador")
                        .HasConstraintName("FK_agregadores_ativos_agregadores_id_agregador"),
                    j =>
                    {
                        j.HasKey("id_agregador", "id_ativo");
                        j.ToTable("agregadores_ativos", "zinpag");
                    }
                );

            // Chaves compostas
            modelBuilder.Entity<PessoaEndereco>()
                .HasKey(pe => new { pe.IdPessoa, pe.IdEndereco });
            modelBuilder.Entity<PessoaDadoBancario>()
                .HasKey(pd => new { pd.IdPessoa, pd.IdDadoBancario });
            modelBuilder.Entity<PessoaContato>()
                .HasKey(pc => new { pc.IdPessoa, pc.IdContato });
        }
    }
}