﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Serilog;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Helpers;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.Importacoes;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.Repositorios.Importacoes;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Helpers.Clientes;

namespace Zin.Application.Services.Importacoes
{
    public class ImportacaoService(
        IAgregadorRepository agregadorRepositorio,
        IDocumentoRepository documentoRepositorio,
        IHttpContextAccessor httpContextAccessor,
        IImportacaoRepository importacaoRepository,
        IItemRepository itemRepositorio,
        IItemVersaoService itemVersaoService,
        IMapper mapper,
        IMovimentacoesRepository movimentacoesRepository,
        IPessoaRepository pessoaRepositorio,
        IVeiculoRepository veiculoRepositorio) : IImportacaoService
    {
        // Corrigir assinatura do método conforme interface
        public async Task<int> ImportarAgregadorAsync(CriaImportacaoAgregadorDTO dto)
        {
            Log.Logger.Information("Iniciando importação de agregador: {@Dto}", dto);

            if (ImportacaoHelper.ImportacaoValida(dto))
            {
                try
                {
                    var dtoJson = JsonConvert.SerializeObject(dto);

                    var importacaoAgregador = new ImportacaoAgregador
                    {
                        DadosAgregador = dtoJson,
                        Status = StatusImportacao.AguardandoProcessamento,
                        DataImportacao = DateTime.UtcNow,
                        IdCliente = dto.IdCliente
                    };
                    var id = await importacaoRepository.InserirAsync(importacaoAgregador);

                    Log.Logger.Information("Importação de agregador concluída com sucesso. ID: {Id}", id);

                    return id;
                }
                catch (Exception ex)
                {
                    Log.Logger.Error(ex, "Erro ao importar agregador: {@Dto}", dto);
                    throw new Exception($"Erro ao importar agregador: {ex.Message}", ex);
                }
            }

            Log.Logger.Error("Importação inválida para o DTO: {@Dto}", dto);
            throw new Exception($"Erro ao importar agregador");
        }

        public async Task ProcessaImportacaoAgregadorAsync()
        {
            Log.Logger.Information("Iniciando processamento de importações pendentes...");

            var clienteSelecionado = ClienteSelecionadoHelper.BuscarClienteSelecionado(httpContextAccessor.HttpContext!);
            if (clienteSelecionado == null || string.IsNullOrEmpty(clienteSelecionado.Id))
                throw new ArgumentNullException(nameof(clienteSelecionado.Id), "Id do cliente não pode ser nulo.");

            var idCliente = clienteSelecionado.Id;

            var importacoesPendentes = await importacaoRepository.BuscarImportacoesPendentesPorClienteAsync(idCliente);
            var idAgregadorGerado = 0;

            foreach (var importacaoPendente in importacoesPendentes)
            {
                try
                {
                    var importacaoDto = JsonConvert.DeserializeObject<CriaImportacaoAgregadorDTO>(importacaoPendente.DadosAgregador);

                    var agregadorExistente = await agregadorRepositorio.BuscarPorNumeroAsync(importacaoDto!.Numero);

                    if (agregadorExistente != null)
                    {
                        mapper.Map(importacaoDto, agregadorExistente);

                        agregadorExistente.IdClienteEmpresa = (await ObterOuCriarClienteEmpresaAsync(importacaoDto)).Id;

                        var listaOficina = await ObterOuCriarOficinas(importacaoDto);

                        var listaFornecedor = await ObterOuCriarFornecedoresAsync(importacaoDto);

                        agregadorExistente.Ativos = await ObterOuCriarAtivosAsync(importacaoDto);

                        agregadorExistente.Documentos = await ObterOuAtualizarDocumentosAsync(importacaoDto);

                        agregadorExistente.Itens = await GerenciarItensEVersoesAsync(importacaoDto.Itens);

                        await agregadorRepositorio.AtualizarAsync(agregadorExistente);

                        var atualizado = await agregadorRepositorio.BuscarPorIdAsync(agregadorExistente.Id);

                        idAgregadorGerado = atualizado != null ? atualizado.Id : agregadorExistente.Id;
                    }
                    else
                    {
                        // Criação normal
                        // TODO: Criar um AgregadorBuilder para isolar as logicas de criação
                        var clienteEmpresa = await ObterOuCriarClienteEmpresaAsync(importacaoDto);
                        var listaOficina = await ObterOuCriarOficinas(importacaoDto);
                        var listaFornecedor = await ObterOuCriarFornecedoresAsync(importacaoDto);
                        var listAtivo = await ObterOuCriarAtivosAsync(importacaoDto);
                        var listaItem = await GerenciarItensEVersoesAsync(importacaoDto.Itens);
                        var listaNota = await ObterOuAtualizarDocumentosAsync(importacaoDto);
                        var listaMovimentacoes = await ObterOuAtualizarMovimentacoes(listaItem);

                        var agregador = mapper.Map<Agregador>(importacaoDto);
                        agregador.IdClienteEmpresa = clienteEmpresa.Id;
                        agregador.ClienteEmpresa = null;
                        agregador.Ativos = listAtivo;
                        agregador.Itens = listaItem;
                        agregador.Documentos = listaNota;
                        agregador.Movimentacoes = listaMovimentacoes;

                        var idGerado = await agregadorRepositorio.InserirAsync(agregador);
                        idAgregadorGerado = idGerado;
                    }

                    importacaoPendente.DataProcessamento = DateTime.UtcNow;
                    importacaoPendente.Status = StatusImportacao.Processado;
                    importacaoPendente.IdAgregador = idAgregadorGerado;

                    await importacaoRepository.AtualizarAsync(importacaoPendente);
                }
                catch (Exception ex)
                {
                    Log.Logger.Error(ex, "Erro ao processar importação: {@ImportacaoPendente}", importacaoPendente);

                    throw new Exception($"Erro ao processar importação: {ex.Message}", ex);
                }
            }

            Log.Logger.Information("Processamento de importações pendentes concluído.");
        }

        private async Task<List<Movimentacao>> ObterOuAtualizarMovimentacoes(List<Item> itensDaImportacao)
        {
            var movimentacoes = new List<Movimentacao>();

            // Agrupar itens por documento. Cada grupo virará uma movimentação.
            var itensAgrupadosPorDocumento = itensDaImportacao
                .Where(i => i.Versoes.Any(v => v.IdDocumento.HasValue))
                .GroupBy(i => i.Versoes.First(v => v.IdDocumento.HasValue).IdDocumento.Value)
                .ToList();

            // Cria ou atualiza movimentação apartir itens agrupados por documento.
            foreach (var grupo in itensAgrupadosPorDocumento)
            {
                var movimentacaoExistente = movimentacoesRepository
                    .BuscarAsync(m => m.IdDocumento == grupo.Key);

                var primeiroItem = grupo.First();
                var primeiraVersao = primeiroItem.Versoes.First(v => v.IdDocumento.HasValue);
                var movimentacao = new Movimentacao
                {
                    IdAgregador = primeiroItem.IdAgregador,
                    IdDocumento = primeiraVersao.IdDocumento,
                    DataHoraAutorizacao = primeiraVersao.DataHoraAutorizacao,
                    //DataHoraMovimento = DateTime.UtcNow,
                    ItensVersoes = grupo.SelectMany(i => i.Versoes).ToList()
                };
                movimentacoes.Add(movimentacao);
            }

            // Agrupar itens sem documento por data de autorização. Cada grupo virará uma movimentação.
            var itensSemDocumentoAgrupadosPorDataAutorizacao = itensDaImportacao
                .Where(i => !i.Versoes.Any(v => v.IdDocumento.HasValue))
                .GroupBy(i => i.Versoes.First().DataHoraAutorizacao?.Date)
                .ToList();

            // Criar movimentação para cada grupo de itens sem documento.
            foreach (var grupo in itensSemDocumentoAgrupadosPorDataAutorizacao)
            {
                var primeiroItem = grupo.First();
                var movimentacao = new Movimentacao
                {
                    IdAgregador = primeiroItem.IdAgregador,
                    DataHoraAutorizacao = primeiroItem.Versoes.First().DataHoraAutorizacao,
                    //DataHoraMovimento = DateTime.UtcNow,
                    ItensVersoes = grupo.SelectMany(i => i.Versoes).ToList()
                };
                movimentacoes.Add(movimentacao);
            }

            return movimentacoes;
        }

        private async Task<ClienteEmpresa> ObterOuCriarClienteEmpresaAsync(CriaImportacaoAgregadorDTO dto)
        {
            var clienteExistente = await pessoaRepositorio.BuscarAsync(x =>
                x.GetType() == typeof(ClienteEmpresa) && ((ClienteEmpresa)x).Cnpj == dto.ClienteEmpresa.Cnpj);

            if (clienteExistente.Any())
            {
                return (ClienteEmpresa)clienteExistente.First();
            }
            else
            {
                if (dto.ClienteEmpresa is null ||
                    string.IsNullOrWhiteSpace(dto.ClienteEmpresa.RazaoSocial) ||
                    string.IsNullOrWhiteSpace(dto.ClienteEmpresa.NomeFantasia) ||
                    string.IsNullOrWhiteSpace(dto.ClienteEmpresa.Cnpj))
                {
                    throw new Exception("ClienteEmpresa não encontrado e dados insuficientes para criação.");
                }

                var clienteEmpresa = new ClienteEmpresa
                {
                    Cnpj = dto.ClienteEmpresa.Cnpj,
                    RazaoSocial = dto.ClienteEmpresa.RazaoSocial,
                    NomeFantasia = dto.ClienteEmpresa.NomeFantasia,
                    TipoPessoa = TipoPessoa.Juridica
                };

                await pessoaRepositorio.InserirAsync(clienteEmpresa);
                return clienteEmpresa;
            }
        }

        private async Task<List<Pessoa>> ObterOuCriarFornecedoresAsync(CriaImportacaoAgregadorDTO dto)
        {
            var listaFornecedor = new List<Pessoa>();
            if (dto.Fornecedores != null && dto.Fornecedores.Any())
            {
                foreach (var fornecedorDto in dto.Fornecedores)
                {
                    if (string.IsNullOrWhiteSpace(fornecedorDto.Cnpj))
                        continue;

                    var cnpjLimpo = new string(fornecedorDto.Cnpj.Where(char.IsDigit).ToArray());

                    var fornecedorExistente = await pessoaRepositorio.BuscarAsync(
                        x => x.GetType() == typeof(PessoaJuridica) && ((PessoaJuridica)x).Cnpj == cnpjLimpo);

                    if (fornecedorExistente.Any())
                    {
                        listaFornecedor.Add(fornecedorExistente.First());
                    }
                    else
                    {
                        var novaPessoaJuridica = new PessoaJuridica
                        {
                            Cnpj = cnpjLimpo,
                            RazaoSocial = fornecedorDto.RazaoSocial,
                            NomeFantasia = fornecedorDto.NomeFantasia,
                            TipoPessoa = TipoPessoa.Juridica
                        };
                        await pessoaRepositorio.InserirAsync(novaPessoaJuridica);
                        listaFornecedor.Add(novaPessoaJuridica);
                    }
                }
            }
            return listaFornecedor;
        }

        private async Task<List<Pessoa>> ObterOuCriarOficinas(CriaImportacaoAgregadorDTO dto)
        {
            if (dto.Oficinas == null || !dto.Oficinas.Any())
                throw new ArgumentException("Oficinas não pode ser nula.", nameof(dto.Oficinas));

            var listaOficinas = new List<Pessoa>();

            if (dto.Oficinas != null && dto.Oficinas.Any())
            {
                foreach (var oficinaDto in dto.Oficinas)
                {
                    if (string.IsNullOrWhiteSpace(oficinaDto.Cnpj))
                        continue;

                    var oficinaExistente = await pessoaRepositorio.BuscarAsync(
                        x => x.GetType() == typeof(PessoaJuridica) && ((PessoaJuridica)x).Cnpj == oficinaDto.Cnpj);

                    if (oficinaExistente.Any())
                    {
                        listaOficinas.Add(oficinaExistente.First());
                    }
                    else
                    {
                        var novaPessoaJuridica = new PessoaJuridica
                        {
                            Cnpj = oficinaDto.Cnpj,
                            RazaoSocial = oficinaDto.RazaoSocial,
                            NomeFantasia = oficinaDto.NomeFantasia,
                            TipoPessoa = TipoPessoa.Juridica
                        };
                        await pessoaRepositorio.InserirAsync(novaPessoaJuridica);

                        listaOficinas.Add(novaPessoaJuridica);
                    }
                }
            }
            return listaOficinas;
        }

        private async Task<List<Ativo>> ObterOuCriarAtivosAsync(CriaImportacaoAgregadorDTO dto)
        {
            var listAtivo = new List<Ativo>();

            var tipoAtivo = dto.Ativos.FirstOrDefault()?.TipoAtivo ?? TipoAtivo.Desconhecido;
            switch (tipoAtivo)
            {
                case TipoAtivo.Desconhecido:
                    throw new ArgumentException("Tipo de ativo desconhecido.");
                case TipoAtivo.Veiculo:
                    if (dto.Ativos != null && dto.Ativos.Any())
                    {
                        foreach (var veiculoDto in dto.Ativos)
                        {
                            if (veiculoDto.Veiculos == null || !veiculoDto.Veiculos.Any())
                                continue;

                            foreach (var vDto in veiculoDto.Veiculos)
                            {
                                var ativosExistentes = await veiculoRepositorio.BuscaVeiculoPorPlacaAsync(vDto.Placa);
                                var ativoExistente = ativosExistentes.FirstOrDefault();

                                Ativo ativo;

                                if (ativoExistente != null)
                                {
                                    ativo = ativoExistente;
                                }
                                else
                                {
                                    ativo = new Veiculo
                                    {
                                        Placa = vDto.Placa,
                                        Chassi = vDto.Chassi,
                                        Modelo = vDto.Modelo,
                                        AnoFabricacao = vDto.AnoFabricacao,
                                        AnoModelo = vDto.AnoModelo
                                    };

                                    await veiculoRepositorio.InserirAsync((Veiculo)ativo);
                                }

                                listAtivo.Add(ativo);
                            }
                        }
                    }
                    break;
                default:
                    throw new ArgumentException("Tipo de ativo desconhecido.");
            }
            return listAtivo;
        }

        private async Task<List<Documento>> ObterOuAtualizarDocumentosAsync(CriaImportacaoAgregadorDTO dto)
        {
            var listaNota = new List<Documento>();
            if (dto.Documentos != null && dto.Documentos.Any())
            {
                foreach (var documentoDto in dto.Documentos)
                {
                    if (!string.IsNullOrEmpty(documentoDto.NumeroDocumento))
                    {
                        var documentoExistente = await documentoRepositorio.BuscarPorNumero(documentoDto.NumeroDocumento);

                        var cnpjFornecedor = new string((documentoDto.CnpjFornecedor.Where(char.IsDigit).ToArray()));
                        var fornecedor = await pessoaRepositorio.BuscarFornecedorPorCnpjAsync(cnpjFornecedor);

                        var cliente = await pessoaRepositorio.BuscarFornecedorPorCnpjAsync(dto.ClienteEmpresa.Cnpj);

                        if (documentoExistente == null)
                        {
                            var novoDocumento = mapper.Map<Documento>(documentoDto);

                            novoDocumento.IdPessoaDestinatario = cliente!.Id;
                            novoDocumento.IdPessoaEmitente = fornecedor!.Id;
                            if (novoDocumento.DataEmissao.Kind != DateTimeKind.Utc)
                                novoDocumento.DataEmissao = DateTime.SpecifyKind(novoDocumento.DataEmissao, DateTimeKind.Utc);

                            listaNota.Add(novoDocumento);
                        }
                        else
                        {
                            mapper.Map(documentoDto, documentoExistente);

                            if (documentoExistente.DataEmissao.Kind != DateTimeKind.Utc)
                                documentoExistente.DataEmissao = DateTime.SpecifyKind(documentoExistente.DataEmissao, DateTimeKind.Utc);

                            await documentoRepositorio.AtualizarAsync(documentoExistente);
                        }
                    }
                }
            }
            return listaNota;
        }

        private async Task<List<Item>> GerenciarItensEVersoesAsync(IList<ImportarItensDTO> itensDto)
        {
            var listaItem = new List<Item>();
            if (itensDto == null || !itensDto.Any())
                return listaItem;

            foreach (var itemDto in itensDto)
            {
                var itemExistente = await itemRepositorio.BuscarPorCodigoAsync(itemDto.Codigo);

                var cnpjFornecedor = new string((itemDto.CnpjFornecedor.Where(char.IsDigit).ToArray()));
                var fornecedores = await pessoaRepositorio.BuscarAsync(
                    x => x.GetType() == typeof(PessoaJuridica) && ((PessoaJuridica)x).Cnpj == cnpjFornecedor);
                var fornecedor = fornecedores.FirstOrDefault();

                var veiculos = await veiculoRepositorio.BuscaVeiculoPorPlacaAsync(itemDto.VeiculoPlaca);
                var veiculo = veiculos.FirstOrDefault();

                var oficinas = await pessoaRepositorio.BuscarAsync(
                    x => x.GetType() == typeof(PessoaJuridica) && ((PessoaJuridica)x).Cnpj == itemDto.CnpjOficina);
                var oficina = oficinas.FirstOrDefault();

                if (itemExistente == null)
                {
                    var novoItem = mapper.Map<Item>(itemDto);
                    var novaVersao = await CriarItemVersaoAsync(novoItem, itemDto);

                    if (novaVersao != null)
                    {
                        novaVersao.IdPessoaFornecedora = fornecedor != null ? fornecedor.Id : 0;
                        novaVersao.Ativo = veiculo;
                        novaVersao.IdOficina = oficina != null ? oficina.Id : 0;

                        novoItem.Versoes.Add(novaVersao);
                    }

                    listaItem.Add(novoItem);
                }
                else
                {
                    var deveCriarNovaVersaoResponse = itemVersaoService.DeveCriarNovaVersao(itemExistente.Versoes, itemDto);
                    if (deveCriarNovaVersaoResponse.DeveCriarNovaVersao)
                    {
                        var novaVersao = await CriarItemVersaoAsync(itemExistente, itemDto);

                        if(deveCriarNovaVersaoResponse.VersaoAnterior != null)
                        {
                            novaVersao.VersaoAnterior = deveCriarNovaVersaoResponse.VersaoAnterior;
                            novaVersao.NumeroVersao = deveCriarNovaVersaoResponse.VersaoAnterior.NumeroVersao + 1;
                        }
                        if (novaVersao != null)
                        {
                            novaVersao.IdPessoaFornecedora = fornecedor != null ? fornecedor.Id : 0;
                            itemExistente.Versoes.Add(novaVersao);
                        }
                    }
                    mapper.Map(itemDto, itemExistente);
                }
            }
            return listaItem;
        }

        private async Task<ItemVersao> CriarItemVersaoAsync(Item item, ImportarItensDTO itemDto)
        {
            if (string.IsNullOrWhiteSpace(itemDto.CnpjOficina))
                return null;

            var oficinas = await pessoaRepositorio.BuscarAsync(
                x => x.GetType() == typeof(PessoaJuridica) && ((PessoaJuridica)x).Cnpj == itemDto.CnpjOficina);
            var oficina = oficinas.FirstOrDefault();

            var veiculos = await veiculoRepositorio.BuscaVeiculoPorPlacaAsync(itemDto.VeiculoPlaca);
            var veiculo = veiculos.FirstOrDefault();

            if (oficina == null)
                return null;

            return new ItemVersao
            {
                IdItem = item.Id,                
                DataHoraAutorizacao = itemDto.DataAutorizacao.ToUniversalTime(),
                ValorUnitario = itemDto.ValorUnitario,
                Quantidade = itemDto.Quantidade,
                ValorTotal = itemDto.ValorTotal,
                DataCriacao = DateTime.UtcNow,
                Oficina = oficina,
                Ativo = veiculo
            };
        }
    }
    
    public class ItemProfile : Profile
    {
        public ItemProfile()
        {
            CreateMap<CriaImportacaoAgregadorDTO, Agregador>();
            CreateMap<CriaImportacaoAgregadorDTO, Agregador>().ReverseMap();
            CreateMap<ImportarItensDTO, Item>();
            CreateMap<ImportarItensDTO, ImportarItensDTO>(); 
            CreateMap<ImportarDocumentoDTO, Documento>();
            CreateMap<ImportarDocumentoDTO, Documento>().ReverseMap();
        }
    }
}
