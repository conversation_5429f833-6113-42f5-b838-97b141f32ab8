﻿using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.DadosBancarios;

namespace Zin.Domain.Entidades.Cadastros.Pessoas
{
    [Table("pessoas_dados_bancarios", Schema = "cadastros")]
    public class PessoaDadoBancario
    {
        [Column("id_pessoa")]
        public int IdPessoa { get; set; }

        [ForeignKey(nameof(IdPessoa))]
        public Pessoa? Pessoa { get; set; }

        [Column("id_dado_bancario")]
        public int IdDadoBancario { get; set; }

        [ForeignKey(nameof(IdDadoBancario))]
        public DadoBancario? DadoBancario { get; set; }

        [Column("principal")]
        public bool Principal { get; set; }
    }
}
