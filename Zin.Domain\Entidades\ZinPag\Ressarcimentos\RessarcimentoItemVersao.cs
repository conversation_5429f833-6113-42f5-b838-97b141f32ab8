﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Domain.Entidades.ZinPag.Ressarcimentos
{
    [Table("ressarcimentos_itens_versao", Schema = "zinpag")]
    public class RessarcimentoItemVersao
    {
        [Key]
        [Column("id_ressarcimento_item_versao")]
        public int Id { get; set; }

        [Column("id_ressarcimento")]
        public int IdRessarcimento { get; set; }

        [Column("id_item_versao")]
        public int IdItemVersao { get; set; }
        
        // Propriedades de navegação

        [ForeignKey(nameof(IdRessarcimento))]
        public virtual Ressarcimento? Ressarcimento { get; set; }

        [ForeignKey(nameof(IdItemVersao))]
        public virtual ItemVersao? ItemVersao { get; set; }
    }
}
