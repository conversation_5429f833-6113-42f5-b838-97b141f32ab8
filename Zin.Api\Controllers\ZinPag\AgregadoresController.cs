using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Agregadores;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Api.Controllers.ZinPag
{
    [ApiController]
    [Route("agregadores")]
    public class AgregadoresController(IAgregadorService agregadorService) : ControllerBase
    {
        private readonly IAgregadorService _agregadorService = agregadorService;

        /// <summary>
        /// Lista todos os agregadores.
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Listar()
        {
            var listaAgregadores = await _agregadorService.ListarAgregadoresAsync();
            return Ok(listaAgregadores);
        }

        /// <summary>
        /// Busca um agregador por ID.
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> Buscar(int id)
        {
            var agregador = await _agregadorService.ObterAgregadorPorIdAsync(id);
            if (agregador == null)
                return NotFound($"Agregador com ID {id} não encontrado.");

            return Ok(agregador);
        }

        /// <summary>
        /// Cria um novo agregador.
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] CriaAgregadorDto dto)
        {
            var novoAgregador = await _agregadorService.CriarAgregadorAsync(dto);
            return CreatedAtAction(nameof(Buscar), new { id = novoAgregador.Id }, novoAgregador);
        }

        /// <summary>
        /// Atualiza um agregador existente.
        /// </summary>
        [HttpPut("{id}")]
        public async Task<IActionResult> Atualizar(int id, [FromBody] AtualizaAgregadorDto dto)
        {
            await _agregadorService.AtualizarAgregadorAsync(id, dto);
            return NoContent();
        }

        /// <summary>
        /// Remove um agregador por ID.
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Deletar(int id)
        {
            await _agregadorService.RemoverAgregadorAsync(id);
            return NoContent();
        }
    }
}