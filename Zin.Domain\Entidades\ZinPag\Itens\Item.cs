﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Entidades.ZinPag.Itens
{
    [Table("itens", Schema = "zinpag")]
    public class Item
    {
        // 1. Chave primária
        [Key]
        [Column("id_item")]
        public int Id { get; set; }

        // 2. <PERSON><PERSON> estrangeira<PERSON>
        [Column("id_agregador")]
        public int IdAgregador { get; set; }

        // 3. Propriedades simples
        [Column("codigo")]
        public required string Codigo { get; set; }

        [Column("descricao")]
        public string? Descricao { get; set; }

        [Column("quantidade")]
        public int Quantidade { get; set; }

        [Column("valor_unitario")]
        public decimal ValorUnitario { get; set; }

        [Column("valor_total")]
        public decimal ValorTotal { get; set; }        

        [Column("status_processamento")]
        public StatusProcessamento StatusProcessamento { get; set; }

        [Column("status_processamento_item_duplicado")]
        public StatusProcessamento StatusProcessamentoItemDuplicado { get; set; }

        [Column("status_processamento_pagamento_duplicado")]
        public StatusProcessamento StatusProcessamentoPagamentoDuplicado { get; set; }

        [Column("status_processamento_pagamento")]
        public StatusProcessamento StatusProcessamentoPagamento { get; set; }

        [Column("status_processamento_ressarcimento")]
        public StatusProcessamento StatusProcessamentoRessarcimento { get; set; }

        // 4. Propriedades de navegação
        [ForeignKey(nameof(IdAgregador))]
        public Agregador? Agregador { get; set; }

        public ICollection<ItemVersao> Versoes { get; set; } = [];
    }
}
