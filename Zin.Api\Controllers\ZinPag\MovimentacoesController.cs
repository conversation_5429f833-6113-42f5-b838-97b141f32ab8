using Microsoft.AspNetCore.Mvc;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Api.Controllers.ZinPag
{
    [ApiController]
    [Route("movimentacoes")]
    public class MovimentacoesController(IMovimentacoesService movimentacoesService, IFornecedorService fornecedorService, IOficinaService oficinaService) : ControllerBase
    {
        private readonly IMovimentacoesService _movimentacoesService = movimentacoesService;
        private readonly IFornecedorService _fornecedorService = fornecedorService;
        private readonly IOficinaService _oficinaService = oficinaService;

        [HttpGet]
        public async Task<IActionResult> Listar()
        {
            var listaAgregadores = await _movimentacoesService.ListarMovimentacoesAsync();
            return Ok(listaAgregadores);
        }

        [HttpGet("{idMovimentacao}")]
        public async Task<IActionResult> Detalhe(int idMovimentacao)
        {
            try
            {
                var detalhe = await _movimentacoesService.DetalheMovimentacao(idMovimentacao);
                return Ok(detalhe);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno: {ex.Message}");
            }
        }

        /// <summary>
        /// Busca os detalhes de uma movimentação por ID do agregador.
        /// </summary>
        [HttpGet("{id}/detalhes-movimentacao")]
        public async Task<IActionResult> ObterDetalhesMovimentacao(int id)
        {
            var detalhes = await _movimentacoesService.ObterDetalhesMovimentacaoAsync(id);
            if (detalhes == null)
                return NotFound($"Detalhes da movimentação para o agregador com ID {id} não encontrados.");

            return Ok(detalhes);
        }

        /// <summary>
        /// Busca itens de outras movimentações dentro do mesmo agregador, excluindo a movimentação especificada.
        /// </summary>
        [HttpGet("{idAgregador}/itens")]
        public async Task<IActionResult> ObterItensOutrasMovimentacoes(int idAgregador, [FromQuery] int excluirMovimentacao)
        {
            try
            {
                var itens = await _movimentacoesService.ObterItensOutrasMovimentacoesAsync(idAgregador, excluirMovimentacao);
                return Ok(itens);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno: {ex.Message}");
            }
        }

        /// <summary>
        /// Solicita ao fornecedor que informe ou atualize seus dados bancários.
        /// </summary>
        [HttpPost("/fornecedores/{fornecedorId}/solicitar-dados-bancarios")]
        public async Task<IActionResult> SolicitarDadosBancarios(int fornecedorId)
        {
            try
            {
                await _fornecedorService.SolicitarDadosBancariosAsync(fornecedorId);
                return Ok(new { message = "Solicitação de dados bancários enviada com sucesso." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = $"Ocorreu um erro ao solicitar os dados bancários: {ex.Message}" });
            }
        }

        /// <summary>
        /// Solicita à oficina a atualização da situação do veículo.
        /// </summary>
        [HttpPost("/oficinas/{oficinaId}/solicitar-atualizacao-situacao")]
        public async Task<IActionResult> SolicitarAtualizacaoSituacao(int oficinaId)
        {
            try
            {
                await _oficinaService.SolicitarAtualizacaoSituacaoAsync(oficinaId);
                return Ok(new { message = "Solicitação de atualização da situação do veículo enviada com sucesso." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = $"Ocorreu um erro ao solicitar a atualização da situação: {ex.Message}" });
            }
        }
    }
}
