﻿using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.Extensions.Configuration;

namespace Smart.Web.Api.Helpers
{
	public static class KeyVaultHelper
	{
		public static SecretClient CreateSecretClient(IConfiguration configuration, Serilog.ILogger logger)
		{
			var keyVaultURL = configuration["KeyVaultURL"] ?? string.Empty;

			string clientId = Environment.GetEnvironmentVariable("AZURE_CLIENT_ID") ?? string.Empty;
			string clientSecret = Environment.GetEnvironmentVariable("AZURE_CLIENT_SECRET") ?? string.Empty;
			string tenantId = Environment.GetEnvironmentVariable("AZURE_TENANT_ID") ?? string.Empty;

			if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret) || string.IsNullOrEmpty(tenantId))
			{
				throw new InvalidOperationException("As variáveis de ambiente para autenticação do Azure Key Vault não estão definidas.");
			}

			var credential = new ClientSecretCredential(tenantId, clientId, clientSecret);

			var secretClient = new SecretClient(new Uri(keyVaultURL), credential);
			return secretClient;
		}
	}
}
