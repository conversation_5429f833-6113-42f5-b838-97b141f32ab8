﻿using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Application.Services.ZinPag.Interfaces
{
    public interface IItemVersaoService
    {
        Task<ItemVersaoDto> CriarVersaoAsync(CriaItemVersaoDto dto);
        Task<IEnumerable<ItemVersaoDto>> ListarVersoesPorItemAsync(int idItem);
        DeveCriarNovaVersaoResponse DeveCriarNovaVersao(ICollection<ItemVersao> todasVersoes, ImportarItensDTO itemDto);
    }
}
