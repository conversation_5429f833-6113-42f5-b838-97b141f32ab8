﻿using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Api.Controllers.ZinPag
{
    [ApiController]
    [Route("api/itens-versoes")]
    public class ItensVersoesController(IItemVersaoService versaoService) : ControllerBase
    {
        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] CriaItemVersaoDto dto)
            => Ok(await versaoService.CriarVersaoAsync(dto));

        [HttpGet("por-item/{idItem}")]
        public async Task<IActionResult> ListarPorItem(int idItem)
            => Ok(await versaoService.ListarVersoesPorItemAsync(idItem));
    }
}
