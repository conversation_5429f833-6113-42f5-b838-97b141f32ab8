﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Zin.Application.Services.Processos
{
    public interface IProcessosService
    {
        /// <summary>
        /// Executa o processamento de verificação de itens duplicados para um agregador.
        /// </summary>
        Task ProcessaItemDuplicadoAsync();

        /// <summary>
        /// Executa o processamento de verificação de pagamentos duplicados para um agregador.
        /// </summary>
        Task ProcessaPagamentoDuplicadoAsync();

        /// <summary>
        /// Executa o processamento de verificação de possivel pagamentos para um agregador.
        /// </summary>
        Task ProcessaPossivelPagamentoAsync();

        /// <summary>
        /// Executa o processamento de verificação de possivel ressarcimento para um agregador.
        /// </summary>
        Task ProcessaPossivelRessarcimentoAsync();
    }
}
