﻿using AutoMapper;
using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Application.Specifications.ItensVersoes;
using Zin.Application.Specifications.ItensVersoes.Interfaces;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class ItemVersaoService(IItemVersaoRepository itemVersaoRepository, IMapper mapper) : IItemVersaoService
    {
        private readonly IItemVersaoRepository _itemVersaoRepository = itemVersaoRepository;
        private readonly IMapper _mapper = mapper;

        // Lista de especificações que definem as regras para criação de novas versões de itens
        // A ordem das especificações é importante, pois elas serão avaliadas na ordem em que foram adicionadas
        private readonly List<IItemVersaoSpecification> _specifications =
        [
            new AutorizacaoItemSemVersoesSpecification(),
            new ExclusaoComAutorizacaoSpecification(),
            new DataAutorizacaoNovaSpecification(),
            new DataMovimentacaoNovaSpecification(),
            new MesmaDataAutorizacaoQuantidadeDiferenteSpecification(),
        ];

        public async Task<ItemVersaoDto> CriarVersaoAsync(CriaItemVersaoDto dto)
        {
            var entidade = _mapper.Map<ItemVersao>(dto);

            var id = await _itemVersaoRepository.InserirAsync(entidade);
            var versao = await _itemVersaoRepository.BuscarPorIdAsync(id);
            return _mapper.Map<ItemVersaoDto>(versao);
        }

        public async Task<IEnumerable<ItemVersaoDto>> ListarVersoesPorItemAsync(int idItem)
        {
            var versoes = await _itemVersaoRepository.BuscarPorItemIdAsync(idItem);
            return _mapper.Map<IEnumerable<ItemVersaoDto>>(versoes);
        }

        public async Task<ItemVersaoDto> ObterVersaoPorIdAsync(int id)
        {
            var versao = await _itemVersaoRepository.BuscarPorIdAsync(id);
            return _mapper.Map<ItemVersaoDto>(versao);
        }

        public async Task RemoverVersaoAsync(int id)
        {
            await _itemVersaoRepository.DeletarAsync(id);
        }

        public DeveCriarNovaVersaoResponse DeveCriarNovaVersao(ICollection<ItemVersao> todasVersoes, ImportarItensDTO itemDto)
        {
            foreach (var spec in _specifications)
            {
                var specResponse = spec.IsSatisfiedBy(todasVersoes, itemDto);
                if (specResponse.DeveCriarNovaVersao)
                    return specResponse;
            }

            return new DeveCriarNovaVersaoResponse { DeveCriarNovaVersao = false, VersaoAnterior = null };
        }
    }
}
