﻿using AutoMapper;
using Zin.Application.DTOs.Agregadores;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Enums;

namespace Zin.Application.Mappings
{
    public class AgregadorMappingProfile : Profile
    {
        public AgregadorMappingProfile()
        {
            // DTO -> Entidade
            CreateMap<CriaAgregadorDto, Agregador>()
                .ForMember(dest => dest.IdClienteEmpresa, opt => opt.Ignore()) 
                .ForMember(dest => dest.ClienteEmpresa, opt => opt.Ignore())   
                .ForMember(dest => dest.Ativos, opt => opt.Ignore())
                .ForMember(dest => dest.Documentos, opt => opt.Ignore())
                .ForMember(dest => dest.Itens, opt => opt.Ignore())
                .ForMember(dest => dest.Movimentacoes, opt => opt.Ignore())
                .ForMember(dest => dest.TipoAgregador, opt => opt.MapFrom(src => (TipoAgregador)src.TipoAgregador!.Value));


            CreateMap<AtualizaAgregadorDto, Agregador>()
                .ForMember(dest => dest.IdClienteEmpresa, opt => opt.Ignore())
                .ForMember(dest => dest.Ativos, opt => opt.Ignore())
                .ForMember(dest => dest.Documentos, opt => opt.Ignore())
                .ForMember(dest => dest.Itens, opt => opt.Ignore())
                .ForMember(dest => dest.Movimentacoes, opt => opt.Ignore())
                .ForMember(dest => dest.TipoAgregador, opt => opt.MapFrom(src => (TipoAgregador)src.TipoAgregador!.Value));

            // Entidade -> DTO
            CreateMap<Agregador, AgregadorDto>()
                .ForMember(dest => dest.EmpresaCnpj,
                opt => opt.MapFrom(src => src.ClienteEmpresa != null ? src.ClienteEmpresa.Cnpj : null));
        }
    }
}
