﻿using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;

namespace Zin.Application.DTOs.Configuracao
{
    public class AtualizaConfiguracaoDto
    {
        public Guid Id { get; set; }
        public string Nome { get; set; } = string.Empty;
        public TipoProcessamento TipoProcessamento { get; set; }
        public TipoConfiguracao TipoConfiguracao { get; set; }
        public bool Ativo { get; set; }
        public DateTime AtualizadoEm { get; set; }
        public string AlteradoPor { get; set; } = string.Empty;
        public List<AtualizaRegraDto>? Regras { get; set; }
    }
}
