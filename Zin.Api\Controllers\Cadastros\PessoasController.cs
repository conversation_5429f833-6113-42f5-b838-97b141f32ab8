using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Pessoas;
using Zin.Application.Services.Cadastros.Interfaces;

namespace Zin.Api.Controllers.Cadastros
{
    [ApiController]
    [Route("pessoas")]
    public class PessoasController(IPessoaService pessoaService) : ControllerBase
    {
        private readonly IPessoaService _pessoaService = pessoaService;

        [HttpPut("{id}")]
        public async Task<IActionResult> Atualizar(int id, [FromBody] AtualizaPessoaDto pessoaDto)
        {
            await _pessoaService.AtualizarPessoaAsync(id, pessoaDto);
            return Ok($"Pessoa com id {id} atualizada com sucesso.");
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> Buscar(int id)
        {
            var pessoa = await _pessoaService.BuscarPessoaPorIdAsync(id);
            return Ok(pessoa);
        }

        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] CriaPessoaDto dto)
        {
            var id = await _pessoaService.CriaPessoaAsync(dto);
            return Ok(id);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Deletar(int id)
        {
            await _pessoaService.DeletarPessoaAsync(id);
            return Ok(id);
        }

        [HttpGet]
        public async Task<IActionResult> Listar()
        {
            var pessoas = await _pessoaService.ListarPessoasAsync();
            return Ok(pessoas);
        }
    }

}