﻿using AutoMapper;
using Zin.Application.DTOs.Veiculos;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class VeiculoService(
        IAtivoRepository ativoRepository,
        IMapper mapper,
        IVeiculoRepository veiculoRepository) : IVeiculoService
    {
        private readonly IVeiculoRepository _veiculoRepositoriy = veiculoRepository;
        private readonly IAtivoRepository _ativoRepository = ativoRepository;
        private readonly IMapper _mapper = mapper;

        public Task AtualizaVeiculoAsync(int id, AtualizaVeiculoDto dto)
        {
            throw new NotImplementedException();
        }

        public async Task AtualizaStatusVeiculo(AtualizaStatusVeiculoDto veiculoDto)
        {
            if(veiculoDto == null)
            {
                throw new ArgumentNullException(nameof(veiculoDto), "Veículo não pode ser nulo.");
            }

            var veiculoExistente = await _veiculoRepositoriy.BuscarPorIdAsync(veiculoDto.Id);

            if (veiculoExistente == null)
            {
                throw new InvalidOperationException($"Veículo com id {veiculoDto.Id} não encontrado.");
            }
                        
            veiculoExistente.StatusVeiculo = veiculoDto.StatusVeiculo;

            await _veiculoRepositoriy.AtualizarAsync(veiculoExistente);
        }

        public async Task<int> CriarVeiculoAsync(CriaVeiculoDto dto)
        {

            if(dto.Placa == null)
               throw new ArgumentNullException(nameof(dto.Placa), "Placa não pode ser nula.");

            if(dto.TipoAtivo != TipoAtivo.Veiculo)
                throw new ArgumentNullException(nameof(dto.TipoAtivo), "Tipo de Ativo não pode ser nulo.");

            //Cria Ativo
            Ativo ativo = new Veiculo
            {
                Placa = dto.Placa,
                Chassi = dto.Chassi,
                Renavam = dto.Renavam,
                Modelo = dto.Modelo,
                AnoFabricacao = dto.AnoFabricacao,
                AnoModelo = dto.AnoModelo,
                Tipo = dto.TipoAtivo,
                StatusVeiculo = dto.StatusVeiculo
                
            };

            return await _ativoRepository.InserirAsync(ativo);
        }

        public async Task<IEnumerable<VeiculoDto>> ListarVeiculosAsync()
        {
            return _mapper.Map<IEnumerable<Veiculo>, IEnumerable<VeiculoDto>>(await _veiculoRepositoriy.ListarAsync());
        }

        public async Task<VeiculoDto?> ObterVeiculoPorIdAsync(int id)
        {
            var veiculo = await _veiculoRepositoriy.BuscarPorIdAsync(id);

            if (veiculo == null)
            {
                return null;
            }
            return new VeiculoDto
            {
                Id = veiculo.Id,
                Placa = veiculo.Placa
            };
        }

        public Task RemoveVeiculoAsync(int id)
        {
            throw new NotImplementedException();
        }
    }
}
