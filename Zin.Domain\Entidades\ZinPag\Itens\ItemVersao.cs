﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Entidades.ZinPag.Itens
{
    [Table("itens_versoes", Schema = "zinpag")]
    public class ItemVersao
    {
        // 1. Chave primária
        [Key]
        [Column("id_item_versao")]
        public int Id { get; set; }

        // 2. <PERSON><PERSON> estrangeira<PERSON>
        [Column("id_item")]
        public int IdItem { get; set; }

        [Column("id_item_versao_anterior")]
        public int? IdVersaoAnterior { get; set; }

        [Column("id_documento")]
        public int? IdDocumento { get; set; }

        [Column("id_pessoa_fornecedora")]
        public int IdPessoaFornecedora { get; set; }

        [Column("id_ativo")]
        public int IdAtivo { get; set; }

        [Column("id_oficina")]
        public int IdOficina { get; set; }

        [Column("id_movimentacao")]
        public int? IdMovimentacao { get; set; }

        // 3. Propriedades simples
        [Column("numero_versao")]
        public int NumeroVersao { get; set; }

        [Column("valor_unitario")]
        public decimal ValorUnitario { get; set; }

        [Column("quantidade")]
        public int Quantidade { get; set; }

        [Column("valor_total")]
        public decimal ValorTotal { get; set; }

        [Column("data_hora_criacao")]
        public DateTime DataCriacao { get; set; }

        [Column("data_hora_entrega")]
        public DateTime? DataEntrega { get; set; }

        [Column("data_hora_autorizacao")]
        public DateTime? DataHoraAutorizacao { get; set; }

        [Column("data_hora_movimento")]
        public DateTime? DataHoraMovimentacao { get; set; }

        [Column("tipo")]
        public TipoMovimentoItem TipoMovimento { get; set; }

        [Column("status_processamento_item_duplicado")]
        public StatusProcessamento StatusProcessamentoItemDuplicado { get; set; }

        [Column("status_processamento_pagamento_duplicado")]
        public StatusProcessamento StatusProcessamentoPagamentoDuplicado { get; set; }

        [Column("status_processamento_pagamento")]
        public StatusProcessamento StatusProcessamentoPagamento { get; set; }

        [Column("status_processamento_ressarcimento")]
        public StatusProcessamento StatusProcessamentoRessarcimento { get; set; }
        
        [ForeignKey(nameof(IdItem))]
        public Item? Item { get; set; }

        [ForeignKey(nameof(IdVersaoAnterior))]
        public ItemVersao? VersaoAnterior { get; set; }

        [ForeignKey(nameof(IdDocumento))]
        public Documento? Documento { get; set; }

        [ForeignKey(nameof(IdPessoaFornecedora))]
        public Pessoa? PessoaFornecedora { get; set; }

        [ForeignKey(nameof(IdAtivo))]
        public Ativo? Ativo { get; set; }

        [ForeignKey(nameof(IdOficina))]
        public Pessoa? Oficina { get; set; }

        [ForeignKey(nameof(IdMovimentacao))]
        public Movimentacao? Movimentacao { get; set; }
    }
}
