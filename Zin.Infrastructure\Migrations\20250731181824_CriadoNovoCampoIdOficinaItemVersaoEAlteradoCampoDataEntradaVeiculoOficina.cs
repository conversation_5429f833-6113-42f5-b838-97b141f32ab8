﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Zin.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class CriadoNovoCampoIdOficinaItemVersaoEAlteradoCampoDataEntradaVeiculoOficina : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "data_entrada",
                schema: "zinpag",
                table: "veiculo_oficina",
                type: "timestamp with time zone",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone");

            migrationBuilder.AddColumn<int>(
                name: "id_oficina",
                schema: "zinpag",
                table: "itens_versoes",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "id_veiculo",
                schema: "zinpag",
                table: "itens_versoes",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_itens_versoes_id_oficina",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_oficina");

            migrationBuilder.CreateIndex(
                name: "IX_itens_versoes_id_veiculo",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_veiculo");

            migrationBuilder.AddForeignKey(
                name: "FK_itens_versoes_pessoas_id_oficina",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_oficina",
                principalSchema: "cadastros",
                principalTable: "pessoas_juridicas",
                principalColumn: "id_pessoa",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_itens_versoes_veiculos_id_veiculo",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_veiculo",
                principalSchema: "zinpag",
                principalTable: "veiculos",
                principalColumn: "id_ativo",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_itens_versoes_pessoas_id_oficina",
                schema: "zinpag",
                table: "itens_versoes");

            migrationBuilder.DropForeignKey(
                name: "FK_itens_versoes_veiculos_id_veiculo",
                schema: "zinpag",
                table: "itens_versoes");

            migrationBuilder.DropIndex(
                name: "IX_itens_versoes_id_oficina",
                schema: "zinpag",
                table: "itens_versoes");

            migrationBuilder.DropIndex(
                name: "IX_itens_versoes_id_veiculo",
                schema: "zinpag",
                table: "itens_versoes");

            migrationBuilder.DropColumn(
                name: "id_oficina",
                schema: "zinpag",
                table: "itens_versoes");

            migrationBuilder.DropColumn(
                name: "id_veiculo",
                schema: "zinpag",
                table: "itens_versoes");

            migrationBuilder.AlterColumn<DateTime>(
                name: "data_entrada",
                schema: "zinpag",
                table: "veiculo_oficina",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldNullable: true);
        }
    }
}
