﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Zin.Domain.Entidades.Cadastros.Enderecos
{
    [Table("enderecos", Schema = "cadastros")]
    public class Endereco
    {
        [Key]
        [Column("id_endereco")]
        public int Id { get; set; }

        [Column("bairro")]
        public required string Bairro { get; set; }

        [Column("id_cidade")]
        public int IdCidade { get; set; }

        [Column("id_estado")]
        public int IdEstado { get; set; }

        [Column("logradouro")]
        public required string Logradouro { get; set; }

        [Column("numero")]
        public required string Numero { get; set; }

        [Column("complemento")]
        public string? Complemento { get; set; }

        [Column("cep")]
        public required string Cep { get; set; }

        [Column("tipo_endereco")]
        public int TipoEndereco { get; set; }
    }

}
