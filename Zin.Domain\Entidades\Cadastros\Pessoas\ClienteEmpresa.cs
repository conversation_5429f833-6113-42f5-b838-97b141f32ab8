﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.Cadastros.Pessoas
{
    [Table("cliente_empresas", Schema = "cadastros")]
    public class ClienteEmpresa : PessoaJuridica
    {
        [Column("matriz")]
        public bool Matriz { get; set; }

        public ICollection<Agregador> Agregadores { get; set; } = [];
    }
}
