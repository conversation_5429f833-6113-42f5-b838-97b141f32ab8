﻿using Zin.Application.Helpers;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;
using Microsoft.Extensions.Logging;

namespace Zin.Application.Services.Processos
{
    public class PossivelPagamentoDuplicadoService : IPossivelPagamentoDuplicadoService
    {
        private readonly IItemVersaoRepository _itemVersaoRepositorio;
        private readonly IPagamentoRepository _pagamentoRepositorio;
        private readonly IRessarcimentoRepository _ressarcimentoRepositorio;
        private readonly IRegistroProcessamentoRepository _registroProcessamentoRepositorio;
        private readonly IItemRepository _itemRepositorio;
        private readonly ILogger<PossivelPagamentoDuplicadoService> _logger;

        public PossivelPagamentoDuplicadoService(
            IItemVersaoRepository itemVersaoRepositorio,
            IPagamentoRepository pagamentoRepositorio,
            IRessarcimentoRepository ressarcimentoRepositorio,
            IRegistroProcessamentoRepository registroProcessamentoRepositorio,
            IItemRepository itemRepositorio,
            IAgregadorRepository agregadorRepositorio,
            ILogger<PossivelPagamentoDuplicadoService> logger)
        {
            _itemVersaoRepositorio = itemVersaoRepositorio;
            _pagamentoRepositorio = pagamentoRepositorio;
            _ressarcimentoRepositorio = ressarcimentoRepositorio;
            _registroProcessamentoRepositorio = registroProcessamentoRepositorio;
            _itemRepositorio = itemRepositorio;
            _logger = logger;
        }

        public async Task ProcessarAsync(int idAgregador)
        {
            var itensVersao = await _itemVersaoRepositorio.BuscarPorAgregadorIdAsync(idAgregador);

            foreach (var itemVersao in itensVersao)
            {
                try
                {
                    bool duplicidadeDetectada = false;

                    var pagamentos = await _pagamentoRepositorio.BuscarPagamentosPorItemVersaoIdAsync(itemVersao.Id);
                    var ressarcimentos = await _ressarcimentoRepositorio.BuscarPorItemVersaoIdAsync(itemVersao.Id);

                    if (pagamentos.Count() > 1)
                    {
                        var pagamentosVivos = pagamentos.Count() - ressarcimentos.Count();
                        if (pagamentosVivos > 1)
                        {
                            var registro = await _registroProcessamentoRepositorio.BuscarPorItemVersaoTipoAsync(itemVersao.Id, TipoProcessamento.PagamentoDuplicado);
                            if (registro == null || registro.Decisao == null)
                            {
                                await RegistroProcessamentoHelper.RegistrarAsync(
                                    itemVersao,
                                    TipoProcessamento.PagamentoDuplicado,
                                    StatusProcessamento.PossivelmenteDuplicado,
                                    Divergencia.PossivelPagamentoDuplicado,
                                    Condicao.SemCondicao,
                                    "Mais de um pagamento sem ressarcimento.",
                                    _registroProcessamentoRepositorio,
                                    _itemVersaoRepositorio
                                );
                                duplicidadeDetectada = true;
                                continue;
                            }
                        }
                    }

                    var itensVersaoDoAgregador = await _itemVersaoRepositorio.BuscarPorAgregadorIdAsync(idAgregador);
                    var autorizaSemCancelamento = new List<Zin.Domain.Entidades.ZinPag.Itens.ItemVersao>();
                    foreach (var item in itensVersaoDoAgregador.OrderBy(iv => iv.DataCriacao))
                    {
                        if (item.TipoMovimento == TipoMovimentoItem.Autorizacao)
                            autorizaSemCancelamento.Add(item);
                        else if (item.TipoMovimento == TipoMovimentoItem.Exclusao && autorizaSemCancelamento.Any())
                            autorizaSemCancelamento.RemoveAt(autorizaSemCancelamento.Count - 1);
                    }

                    if (autorizaSemCancelamento.Count > 1)
                    {
                        foreach (var ivAutorizado in autorizaSemCancelamento)
                        {
                            var registro = await _registroProcessamentoRepositorio.BuscarPorItemVersaoIdAsync(ivAutorizado.Id);
                            if (registro == null || registro.Decisao == null)
                            {
                                await RegistroProcessamentoHelper.RegistrarAsync(
                                    ivAutorizado,
                                    TipoProcessamento.PagamentoDuplicado,
                                    StatusProcessamento.PossivelmenteDuplicado,
                                    Divergencia.PossivelPagamentoDuplicado,
                                    Condicao.SemCondicao,
                                    "Mais de um item versão autorizado ativo (sem cancelamento correspondente).",
                                    _registroProcessamentoRepositorio,
                                    _itemVersaoRepositorio
                                );
                                duplicidadeDetectada = true;
                                continue;
                            }
                        }
                    }

                    if (!duplicidadeDetectada)
                    {
                        await RegistroProcessamentoHelper.RegistrarAsync(
                            itemVersao,
                            TipoProcessamento.PagamentoDuplicado,
                            StatusProcessamento.Processado,
                            Divergencia.SemDivergencia,
                            Condicao.SemCondicao,
                            "Item processado sem indício de duplicidade.",
                            _registroProcessamentoRepositorio,
                            _itemVersaoRepositorio
                        );
                    }                    
                }
                catch (Exception ex)
                {
                    StatusAtualizador.SetStatusProcessamentoItemVersao(itemVersao, TipoProcessamento.PagamentoDuplicado, StatusProcessamento.Erro);
                    await _itemVersaoRepositorio.AtualizarAsync(itemVersao);

                    if (itemVersao.Item != null)
                    {
                        itemVersao.Item.StatusProcessamento = StatusProcessamento.Erro;
                        await _itemRepositorio.AtualizarAsync(itemVersao.Item);
                    }

                    _logger.LogError(ex, $"Erro ao processar versão {itemVersao.Id} do item {itemVersao.IdItem} no agregador {idAgregador} no processo Possivel Pagamento Duplicado");
                }
                await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
            }
        }
    }
}