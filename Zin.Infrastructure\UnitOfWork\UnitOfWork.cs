﻿using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.EntityFrameworkCore;
using Zin.Infrastructure.Dados;

namespace Zin.Infrastructure.UnitOfWork
{
    public class UnitOfWork : IUnitOfWork, IAsyncDisposable
    {
        private readonly ZinDbContext _context;
        private IDbContextTransaction? _transaction;

        public UnitOfWork(IDbContextFactory<ZinDbContext> contextFactory)
        {
            _context = contextFactory.CreateDbContext();
        }

        public ZinDbContext Context => _context;

        public async Task BeginTransactionAsync()
        {
            if (_transaction != null)
                throw new InvalidOperationException("Transação já iniciada.");
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task<int> CommitAsync()
        {
            try
            {
                var result = await _context.SaveChangesAsync();

                // Commit only if transaction started
                if (_transaction != null)
                {
                    await _transaction.CommitAsync();
                    await DisposeTransactionAsync();
                }

                return result;
            }
            catch
            {
                // Rollback only if transaction started
                if (_transaction != null)
                {
                    await RollbackAsync();
                }
                throw;
            }
        }

        public async Task RollbackAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await DisposeTransactionAsync();
            }
        }

        private async Task DisposeTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }

        public async ValueTask DisposeAsync()
        {
            if (_transaction != null)
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
            await _context.DisposeAsync();
        }
    }
}