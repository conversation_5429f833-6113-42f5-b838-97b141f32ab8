﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class RessarcimentoRepository(IUnitOfWork unitOfWork) : IRessarcimentoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;

        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(Ressarcimento entidade)
        {
            Context.Ressarcimentos.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<Ressarcimento>> BuscarAsync(Expression<Func<Ressarcimento, bool>> predicado)
        {
            return await Context.Ressarcimentos.Where(predicado).ToListAsync();
        }

        public Task<Ressarcimento?> BuscarPorIdAsync(int id)
        {
            return Context.Ressarcimentos.FindAsync(id)
                .AsTask() ?? throw new Exception($"Ressarcimento com ID {id} não encontrado.");
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<Ressarcimento> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<int> InserirAsync(Ressarcimento entidade)
        {
            await Context.Ressarcimentos.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<Ressarcimento> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<IEnumerable<Ressarcimento>> ListarAsync()
        {
            return await Context.Ressarcimentos.ToListAsync();
        }

        public async Task<IEnumerable<Ressarcimento>> BuscarPorItemVersaoIdAsync(int itemVersaoId)
        {
            return await Context.Ressarcimentos
       .Where(r => r.RessarcimentoItemVersoes.Any(riv => riv.IdItemVersao == itemVersaoId))
       .ToListAsync();
        }

        public async Task<bool> ExisteRessarcimentoParaItemVersaoAsync(int itemVersaoId)
        {
            var ressarcimentos = await BuscarPorItemVersaoIdAsync(itemVersaoId);
            return ressarcimentos.Any();
        }

        public async Task<IEnumerable<Ressarcimento>> BuscarPorAgregadorIdAsync(int agregadorId)
        {
            return await Context.Ressarcimentos
                .Where(r => r.IdAgregador == agregadorId)
                .ToListAsync();
        }
    }
}
