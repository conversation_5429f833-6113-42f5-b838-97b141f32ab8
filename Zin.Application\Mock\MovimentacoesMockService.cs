﻿using Zin.Application.DTOs.Movimentacoes;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Application.Mock
{
    public class MovimentacoesMockService : IMovimentacoesService
    {
        public Task<MovimentacaoDetalhesDto> DetalheMovimentacao(int idMovimentacao)
        {
            var detalhe = MovimentacoesMockDb
                .MovimentacoesDetalhes
                .FirstOrDefault(m => m.IdMovimentacao == idMovimentacao);

            return detalhe == null
                ? throw new KeyNotFoundException($"Movimentação com IdAgregador {idMovimentacao} não encontrada.")
                : Task.FromResult(detalhe);
        }

        public Task<List<MovimentacaoDto>> ListarMovimentacoesAsync()
        {            
            return Task.FromResult(MovimentacoesMockDb.Movimentacoes);
        }

        public Task<ObterMovimentacaoDetalhesDto> ObterDetalhesMovimentacaoAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task<List<ItemAgregadorDto>> ObterItensOutrasMovimentacoesAsync(int idAgregador, int movId)
        {
            return Task.FromResult(new List<ItemAgregadorDto>());
        }
    }
}
