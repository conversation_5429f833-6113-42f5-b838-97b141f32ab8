﻿using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Helpers
{
    public static class RegistroProcessamentoHelper
    {
        public static async Task RegistrarAsync(
            ItemVersao itemVersao,
            TipoProcessamento tipoProcessamento,
            StatusProcessamento statusProcessamento,
            Divergencia? divergencia,
            Condicao? condicao,
            string? detalhe,
            IRegistroProcessamentoRepository? registroProcessamentoRepositorio,
            IItemVersaoRepository itemVersaoRepositorio
        )
        {
            StatusAtualizador.SetStatusProcessamentoItemVersao(itemVersao, tipoProcessamento, statusProcessamento);
            await itemVersaoRepositorio.AtualizarAsync(itemVersao);

            if (registroProcessamentoRepositorio == null) return;

            var registroExistente = await registroProcessamentoRepositorio.BuscarPorItemVersaoTipoAsync(itemVersao.Id, tipoProcessamento);

            if (registroExistente == null)
            {
                var registro = new RegistroProcessamentoItemVersao
                {
                    IdItemVersao = itemVersao.Id,
                    Tipo = tipoProcessamento,
                    Status = statusProcessamento,
                    DataAtualizacao = DateTime.UtcNow,
                    Divergencia = divergencia,
                    Condicao = condicao,
                    Detalhe = detalhe,
                    Decisao = null,
                    Usuario = null
                };
                await registroProcessamentoRepositorio.InserirAsync(registro);
            }
            else if (
                registroExistente.Status != statusProcessamento ||
                registroExistente.Divergencia != divergencia ||
                registroExistente.Condicao != condicao ||
                registroExistente.Detalhe != detalhe ||
                registroExistente.Tipo != tipoProcessamento
            )
            {
                registroExistente.Status = statusProcessamento;
                registroExistente.DataAtualizacao = DateTime.UtcNow;
                registroExistente.Divergencia = divergencia;
                registroExistente.Condicao = condicao;
                registroExistente.Detalhe = detalhe;
                registroExistente.Tipo = tipoProcessamento;
                registroExistente.Decisao = null;
                registroExistente.Usuario = null;
                await registroProcessamentoRepositorio.AtualizarAsync(registroExistente);
            }
        }
    }
}