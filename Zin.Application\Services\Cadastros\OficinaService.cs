using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Zin.Application.Configuration;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Application.DTOs.Notifications;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros;

public class OficinaService : IOficinaService
{
    private readonly IPessoaRepository _pessoaRepository;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<OficinaService> _logger;
    private readonly NotificationServiceConfig _notificationServiceConfig;

    public OficinaService(
        IPessoaRepository pessoaRepository,
        IHttpClientFactory httpClientFactory,
        ILogger<OficinaService> logger,
        IOptions<NotificationServiceConfig> notificationServiceConfig)
    {
        _pessoaRepository = pessoaRepository;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
        _notificationServiceConfig = notificationServiceConfig.Value;
    }

    public async Task SolicitarAtualizacaoSituacaoAsync(int oficinaId)
    {
        var oficina = await _pessoaRepository.BuscarComContatosPorIdAsync(oficinaId);

        if (oficina == null)
        {
            _logger.LogWarning("Oficina com ID {OficinaId} não encontrada.", oficinaId);
            throw new Exception($"Oficina com ID {oficinaId} não encontrada.");
        }

        var emailContato = oficina.PessoasContatos
            .FirstOrDefault(pc => pc.Contato.TipoContato == TipoContato.Email && pc.Principal)?.Contato;

        if (emailContato == null)
        {
            _logger.LogError("Oficina com ID {OficinaId} não possui um e-mail principal de contato.", oficinaId);
            throw new Exception("Oficina não possui um e-mail principal de contato.");
        }

        var request = new NotificacaoRequest
        {
            Recipient = emailContato.Valor,
            Subject = $"Solicitação de Atualização da Situação do Veículo",
            Message = $"<p>Prezada oficina,</p><p>Solicitamos que por gentileza atualize a situação do veículo.</p><p>Atenciosamente,</p><p>Sistema Zin.</p>",
            Channel = NotificacaoCanal.Email
        };

        var httpClient = _httpClientFactory.CreateClient();
        try
        {
            var endpoint = new Uri(new Uri(_notificationServiceConfig.BaseUrl), "api/notificacao");
            var response = await httpClient.PostAsJsonAsync(endpoint, request);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Solicitação de atualização de situação para a oficina {OficinaId} enviada com sucesso.", oficinaId);
            }
            else
            {
                var responseBody = await response.Content.ReadAsStringAsync();
                _logger.LogError("Falha ao enviar solicitação de atualização de situação para a oficina {OficinaId}. Status: {StatusCode}, Response: {ResponseBody}", 
                    oficinaId, response.StatusCode, responseBody);
                throw new Exception("Falha ao enviar a notificação.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao comunicar com o serviço de notificação para a oficina {OficinaId}.", oficinaId);
            throw;
        }
    }
}
