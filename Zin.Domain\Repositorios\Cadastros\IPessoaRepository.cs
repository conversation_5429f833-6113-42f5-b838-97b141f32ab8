﻿using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Excecoes;

namespace Zin.Domain.Repositorios.Cadastros
{
    public interface IPessoaRepository : IRepositoryBase<Pessoa, int>
    {
        Task<Pessoa?> BuscarPorDocumentoAsync(string documento);
        Task<Pessoa?> BuscarFornecedorPorCnpjAsync(string cnpj);
        public Task<IEnumerable<PessoaJuridica>> BuscarPorCnpjAsync(string cnpj);
        Task<Pessoa?> BuscarComContatosPorIdAsync(int id);
    }
}
