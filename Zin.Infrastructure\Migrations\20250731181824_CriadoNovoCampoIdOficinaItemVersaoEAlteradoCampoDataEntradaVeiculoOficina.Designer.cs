﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Zin.Infrastructure.Dados;

#nullable disable

namespace Zin.Infrastructure.Migrations
{
    [DbContext(typeof(ZinDbContext))]
    [Migration("20250731181824_CriadoNovoCampoIdOficinaItemVersaoEAlteradoCampoDataEntradaVeiculoOficina")]
    partial class CriadoNovoCampoIdOficinaItemVersaoEAlteradoCampoDataEntradaVeiculoOficina
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("public")
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Contatos.Contato", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_contato");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("nome");

                    b.Property<string>("Observacao")
                        .HasColumnType("text")
                        .HasColumnName("observacao");

                    b.Property<int>("TipoContato")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_contato");

                    b.Property<string>("Valor")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("valor");

                    b.HasKey("Id");

                    b.ToTable("contatos", "cadastros");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.DadosBancarios.DadoBancario", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_dado_bancario");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Agencia")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("agencia");

                    b.Property<string>("Banco")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("banco");

                    b.Property<string>("Conta")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("conta");

                    b.Property<string>("CpfCnpjTitular")
                        .IsRequired()
                        .HasMaxLength(14)
                        .HasColumnType("character varying(14)")
                        .HasColumnName("cpf_cnpj_titular");

                    b.Property<int?>("PessoaId")
                        .HasColumnType("integer");

                    b.Property<bool>("Principal")
                        .HasColumnType("boolean")
                        .HasColumnName("principal");

                    b.Property<int>("TipoConta")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_conta");

                    b.Property<string>("Titular")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("titular");

                    b.HasKey("Id");

                    b.HasIndex("PessoaId");

                    b.ToTable("dados_bancarios", "cadastros");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Enderecos.Cidade", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_cidade");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("IdEstado")
                        .HasColumnType("integer")
                        .HasColumnName("id_estado");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("nome");

                    b.HasKey("Id");

                    b.HasIndex("IdEstado");

                    b.ToTable("cidades", "cadastros");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Enderecos.Endereco", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_endereco");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Bairro")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("bairro");

                    b.Property<string>("Cep")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("cep");

                    b.Property<string>("Complemento")
                        .HasColumnType("text")
                        .HasColumnName("complemento");

                    b.Property<int>("IdCidade")
                        .HasColumnType("integer")
                        .HasColumnName("id_cidade");

                    b.Property<int>("IdEstado")
                        .HasColumnType("integer")
                        .HasColumnName("id_estado");

                    b.Property<string>("Logradouro")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("logradouro");

                    b.Property<string>("Numero")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("numero");

                    b.Property<int>("TipoEndereco")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_endereco");

                    b.HasKey("Id");

                    b.ToTable("enderecos", "cadastros");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Enderecos.Estado", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_estado");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("nome");

                    b.Property<string>("Sigla")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("sigla");

                    b.HasKey("Id");

                    b.ToTable("estados", "cadastros");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_pessoa");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("TipoPessoa")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_pessoa");

                    b.HasKey("Id");

                    b.ToTable("pessoas", "cadastros");

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaContato", b =>
                {
                    b.Property<int>("IdPessoa")
                        .HasColumnType("integer")
                        .HasColumnName("id_pessoa");

                    b.Property<int>("IdContato")
                        .HasColumnType("integer")
                        .HasColumnName("id_contato");

                    b.Property<bool>("Principal")
                        .HasColumnType("boolean")
                        .HasColumnName("principal");

                    b.HasKey("IdPessoa", "IdContato");

                    b.HasIndex("IdContato");

                    b.ToTable("pessoas_contatos", "cadastros");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaDadoBancario", b =>
                {
                    b.Property<int>("IdPessoa")
                        .HasColumnType("integer")
                        .HasColumnName("id_pessoa");

                    b.Property<int>("IdDadoBancario")
                        .HasColumnType("integer")
                        .HasColumnName("id_dado_bancario");

                    b.Property<bool>("Principal")
                        .HasColumnType("boolean")
                        .HasColumnName("principal");

                    b.HasKey("IdPessoa", "IdDadoBancario");

                    b.HasIndex("IdDadoBancario");

                    b.ToTable("pessoas_dados_bancarios", "cadastros");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaEndereco", b =>
                {
                    b.Property<int>("IdPessoa")
                        .HasColumnType("integer")
                        .HasColumnName("id_pessoa");

                    b.Property<int>("IdEndereco")
                        .HasColumnType("integer")
                        .HasColumnName("id_endereco");

                    b.Property<bool>("Principal")
                        .HasColumnType("boolean")
                        .HasColumnName("principal");

                    b.HasKey("IdPessoa", "IdEndereco");

                    b.HasIndex("IdEndereco");

                    b.ToTable("pessoas_enderecos", "cadastros");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Agregadores.Agregador", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_agregador");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("IdClienteEmpresa")
                        .HasColumnType("integer")
                        .HasColumnName("id_cliente_empresa");

                    b.Property<string>("Numero")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("numero");

                    b.Property<int>("StatusProcessamento")
                        .HasColumnType("integer")
                        .HasColumnName("status_processamento");

                    b.Property<int>("TipoAgregador")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_agregador");

                    b.HasKey("Id");

                    b.HasIndex("IdClienteEmpresa");

                    b.ToTable("agregadores", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Ativos.Ativo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_ativo");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Tipo")
                        .HasColumnType("integer")
                        .HasColumnName("tipo");

                    b.HasKey("Id");

                    b.ToTable("ativos", "zinpag");

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Ativos.VeiculoOficina", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_veiculo_oficina");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("DataEntrada")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_entrada");

                    b.Property<DateTime?>("DataSaida")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_saida");

                    b.Property<int>("OficinaId")
                        .HasColumnType("integer")
                        .HasColumnName("id_pessoa_oficina");

                    b.Property<int>("VeiculoId")
                        .HasColumnType("integer")
                        .HasColumnName("id_veiculo");

                    b.Property<bool>("VeiculoPresente")
                        .HasColumnType("boolean")
                        .HasColumnName("veiculo_presente");

                    b.HasKey("Id");

                    b.HasIndex("OficinaId");

                    b.HasIndex("VeiculoId");

                    b.ToTable("veiculo_oficina", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Documentos.Documento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_documento");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CaminhoArquivo")
                        .HasColumnType("text")
                        .HasColumnName("caminho_arquivo");

                    b.Property<DateTime>("DataEmissao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_hora_emissao");

                    b.Property<int>("IdAgregador")
                        .HasColumnType("integer")
                        .HasColumnName("id_agregador");

                    b.Property<int>("IdPessoaDestinatario")
                        .HasColumnType("integer")
                        .HasColumnName("id_pessoa_destinatario");

                    b.Property<int>("IdPessoaEmitente")
                        .HasColumnType("integer")
                        .HasColumnName("id_pessoa_emitente");

                    b.Property<string>("Numero")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("numero");

                    b.Property<int>("TipoDocumento")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_documento");

                    b.Property<decimal>("ValorTotal")
                        .HasColumnType("numeric")
                        .HasColumnName("total");

                    b.HasKey("Id");

                    b.HasIndex("IdAgregador");

                    b.HasIndex("IdPessoaDestinatario");

                    b.HasIndex("IdPessoaEmitente");

                    b.ToTable("documentos", "zinpag");

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Documentos.DocumentoItemVersao", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_documento_item_versao");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("DocumentoId")
                        .HasColumnType("integer");

                    b.Property<int>("IdDocumentoPagamento")
                        .HasColumnType("integer")
                        .HasColumnName("id_documento_pagamento");

                    b.Property<int>("IdItemVersao")
                        .HasColumnType("integer")
                        .HasColumnName("id_item_versao");

                    b.HasKey("Id");

                    b.HasIndex("DocumentoId");

                    b.HasIndex("IdDocumentoPagamento");

                    b.HasIndex("IdItemVersao");

                    b.ToTable("documento_item_versao", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Documentos.DocumentoPagamento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_documento_pagamento");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("IdDocumento")
                        .HasColumnType("integer")
                        .HasColumnName("id_documento");

                    b.Property<int>("IdPagamento")
                        .HasColumnType("integer")
                        .HasColumnName("id_pagamento");

                    b.HasKey("Id");

                    b.HasIndex("IdDocumento")
                        .IsUnique();

                    b.HasIndex("IdPagamento");

                    b.ToTable("documentos_pagamento", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Itens.Item", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_item");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("codigo");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<int>("IdAgregador")
                        .HasColumnType("integer")
                        .HasColumnName("id_agregador");

                    b.Property<int>("Quantidade")
                        .HasColumnType("integer")
                        .HasColumnName("quantidade");

                    b.Property<int>("StatusProcessamento")
                        .HasColumnType("integer")
                        .HasColumnName("status_processamento");

                    b.Property<int>("StatusProcessamentoItemDuplicado")
                        .HasColumnType("integer")
                        .HasColumnName("status_processamento_item_duplicado");

                    b.Property<int>("StatusProcessamentoPagamento")
                        .HasColumnType("integer")
                        .HasColumnName("status_processamento_pagamento");

                    b.Property<int>("StatusProcessamentoPagamentoDuplicado")
                        .HasColumnType("integer")
                        .HasColumnName("status_processamento_pagamento_duplicado");

                    b.Property<int>("StatusProcessamentoRessarcimento")
                        .HasColumnType("integer")
                        .HasColumnName("status_processamento_ressarcimento");

                    b.Property<decimal>("ValorTotal")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_total");

                    b.Property<decimal>("ValorUnitario")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_unitario");

                    b.HasKey("Id");

                    b.HasIndex("IdAgregador");

                    b.ToTable("itens", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Itens.ItemVersao", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_item_versao");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("DataCriacao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_hora_criacao");

                    b.Property<DateTime?>("DataEntrega")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_hora_entrega");

                    b.Property<DateTime?>("DataHoraAutorizacao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_hora_autorizacao");

                    b.Property<DateTime?>("DataHoraMovimentacao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_hora_movimento");

                    b.Property<int?>("IdDocumento")
                        .HasColumnType("integer")
                        .HasColumnName("id_documento");

                    b.Property<int>("IdItem")
                        .HasColumnType("integer")
                        .HasColumnName("id_item");

                    b.Property<int>("IdOficina")
                        .HasColumnType("integer")
                        .HasColumnName("id_oficina");

                    b.Property<int>("IdPessoaFornecedora")
                        .HasColumnType("integer")
                        .HasColumnName("id_pessoa_fornecedora");

                    b.Property<int>("IdVeiculo")
                        .HasColumnType("integer")
                        .HasColumnName("id_veiculo");

                    b.Property<int?>("IdVersaoAnterior")
                        .HasColumnType("integer")
                        .HasColumnName("id_item_versao_anterior");

                    b.Property<int>("NumeroVersao")
                        .HasColumnType("integer")
                        .HasColumnName("numero_versao");

                    b.Property<int>("Quantidade")
                        .HasColumnType("integer")
                        .HasColumnName("quantidade");

                    b.Property<int>("StatusProcessamentoItemDuplicado")
                        .HasColumnType("integer")
                        .HasColumnName("status_processamento_item_duplicado");

                    b.Property<int>("StatusProcessamentoPagamento")
                        .HasColumnType("integer")
                        .HasColumnName("status_processamento_pagamento");

                    b.Property<int>("StatusProcessamentoPagamentoDuplicado")
                        .HasColumnType("integer")
                        .HasColumnName("status_processamento_pagamento_duplicado");

                    b.Property<int>("StatusProcessamentoRessarcimento")
                        .HasColumnType("integer")
                        .HasColumnName("status_processamento_ressarcimento");

                    b.Property<int>("TipoMovimento")
                        .HasColumnType("integer")
                        .HasColumnName("tipo");

                    b.Property<decimal>("ValorTotal")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_total");

                    b.Property<decimal>("ValorUnitario")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_unitario");

                    b.HasKey("Id");

                    b.HasIndex("IdDocumento");

                    b.HasIndex("IdItem");

                    b.HasIndex("IdOficina");

                    b.HasIndex("IdPessoaFornecedora");

                    b.HasIndex("IdVeiculo");

                    b.HasIndex("IdVersaoAnterior");

                    b.ToTable("itens_versoes", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Pagamentos.LiquidacaoPagamento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_liquidacao_pagamento");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Data")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data");

                    b.Property<int>("IdPagamento")
                        .HasColumnType("integer")
                        .HasColumnName("id_pagamento");

                    b.Property<int>("StatusPagamento")
                        .HasColumnType("integer")
                        .HasColumnName("status_pagamento");

                    b.Property<decimal>("ValorPago")
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("valor_pago");

                    b.HasKey("Id");

                    b.HasIndex("IdPagamento");

                    b.ToTable("liquidacoes_pagamentos", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Pagamentos.Pagamento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_pagamento");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("Cancelado")
                        .HasColumnType("boolean")
                        .HasColumnName("cancelado");

                    b.Property<DateTime?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_atualizacao");

                    b.Property<DateTime>("DataCriacao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_criacao");

                    b.Property<DateTime?>("DataPrevisao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_previsao");

                    b.Property<string>("Descricao")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("descricao");

                    b.Property<int>("FormaPagamento")
                        .HasMaxLength(50)
                        .HasColumnType("integer")
                        .HasColumnName("forma_pagamento");

                    b.Property<int?>("IdAgregador")
                        .HasColumnType("integer")
                        .HasColumnName("id_agregador");

                    b.Property<int>("IdPessoaBeneficiaria")
                        .HasColumnType("integer")
                        .HasColumnName("id_pessoa");

                    b.Property<int>("StatusPagamento")
                        .HasMaxLength(20)
                        .HasColumnType("integer")
                        .HasColumnName("status_pagamento");

                    b.Property<decimal>("Valor")
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("valor");

                    b.HasKey("Id");

                    b.HasIndex("IdAgregador");

                    b.HasIndex("IdPessoaBeneficiaria");

                    b.ToTable("pagamentos", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Pagamentos.PagamentoItemVersao", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("IdItemVersao")
                        .HasColumnType("integer")
                        .HasColumnName("id_item_versao");

                    b.Property<int>("IdPagamento")
                        .HasColumnType("integer")
                        .HasColumnName("id_pagamento");

                    b.HasKey("Id");

                    b.HasIndex("IdItemVersao");

                    b.HasIndex("IdPagamento");

                    b.ToTable("pagamento_itens_versoes", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Processamento.ProcessamentoAgregadorStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_processamento_agregador_status");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_atualizacao");

                    b.Property<string>("Detalhe")
                        .HasColumnType("text")
                        .HasColumnName("detalhe");

                    b.Property<int>("IdAgregador")
                        .HasColumnType("integer")
                        .HasColumnName("id_agregador");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<int>("Tipo")
                        .HasColumnType("integer")
                        .HasColumnName("tipo");

                    b.HasKey("Id");

                    b.HasIndex("IdAgregador");

                    b.ToTable("processamento_agregador_status", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Processamento.RegistroProcessamentoItemVersao", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_registro_processamento_item_versao");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("Condicao")
                        .HasColumnType("integer")
                        .HasColumnName("condicao");

                    b.Property<DateTime?>("DataAtualizacao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_atualizacao");

                    b.Property<int?>("Decisao")
                        .HasColumnType("integer")
                        .HasColumnName("decisao");

                    b.Property<string>("Detalhe")
                        .HasColumnType("text")
                        .HasColumnName("detalhe");

                    b.Property<int?>("Divergencia")
                        .HasColumnType("integer")
                        .HasColumnName("divergencia");

                    b.Property<int>("IdItemVersao")
                        .HasColumnType("integer")
                        .HasColumnName("id_item_versao");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<int>("Tipo")
                        .HasColumnType("integer")
                        .HasColumnName("tipo");

                    b.Property<string>("Usuario")
                        .HasColumnType("text")
                        .HasColumnName("usuario");

                    b.HasKey("Id");

                    b.HasIndex("IdItemVersao");

                    b.ToTable("registro_processamento_item_versao", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Ressarcimentos.Ressarcimento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_ressarcimento");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("Cancelado")
                        .HasColumnType("boolean")
                        .HasColumnName("cancelado");

                    b.Property<DateTime?>("DataCancelamento")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_cancelamento");

                    b.Property<DateTime?>("DataPagamento")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_pagamento");

                    b.Property<DateTime>("DataSolicitacao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_solicitacao");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<int>("FormaPagamentoRessarcimento")
                        .HasColumnType("integer")
                        .HasColumnName("forma_pagamento_ressarcimento");

                    b.Property<int>("IdAgregador")
                        .HasColumnType("integer")
                        .HasColumnName("id_agregador");

                    b.Property<int>("IdPessoa")
                        .HasColumnType("integer")
                        .HasColumnName("id_pessoa");

                    b.Property<int>("StatusRessarcimento")
                        .HasColumnType("integer")
                        .HasColumnName("status_ressarcimento");

                    b.Property<decimal>("Valor")
                        .HasColumnType("decimal(12, 2)")
                        .HasColumnName("valor");

                    b.HasKey("Id");

                    b.HasIndex("IdAgregador");

                    b.HasIndex("IdPessoa");

                    b.ToTable("ressarcimentos", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Ressarcimentos.RessarcimentoItemVersao", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id_ressarcimento_item_versao");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("IdItemVersao")
                        .HasColumnType("integer")
                        .HasColumnName("id_item_versao");

                    b.Property<int>("IdRessarcimento")
                        .HasColumnType("integer")
                        .HasColumnName("id_ressarcimento");

                    b.HasKey("Id");

                    b.HasIndex("IdItemVersao");

                    b.HasIndex("IdRessarcimento");

                    b.ToTable("ressarcimentos_itens_versao", "zinpag");
                });

            modelBuilder.Entity("agregadores_ativos", b =>
                {
                    b.Property<int>("id_agregador")
                        .HasColumnType("integer");

                    b.Property<int>("id_ativo")
                        .HasColumnType("integer");

                    b.HasKey("id_agregador", "id_ativo");

                    b.HasIndex("id_ativo");

                    b.ToTable("agregadores_ativos", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaFisica", b =>
                {
                    b.HasBaseType("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa");

                    b.Property<string>("Cpf")
                        .IsRequired()
                        .HasMaxLength(11)
                        .HasColumnType("character varying(11)")
                        .HasColumnName("cpf");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome");

                    b.Property<string>("Sobrenome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("sobrenome");

                    b.ToTable("pessoas_fisicas", "cadastros");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaJuridica", b =>
                {
                    b.HasBaseType("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa");

                    b.Property<string>("Cnpj")
                        .IsRequired()
                        .HasMaxLength(14)
                        .HasColumnType("character varying(14)")
                        .HasColumnName("cnpj");

                    b.Property<string>("NomeFantasia")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome_fantasia");

                    b.Property<string>("RazaoSocial")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)")
                        .HasColumnName("razao_social");

                    b.ToTable("pessoas_juridicas", "cadastros");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Ativos.Veiculo", b =>
                {
                    b.HasBaseType("Zin.Domain.Entidades.ZinPag.Ativos.Ativo");

                    b.Property<int?>("AnoFabricacao")
                        .HasColumnType("integer")
                        .HasColumnName("ano_fabricacao");

                    b.Property<int?>("AnoModelo")
                        .HasColumnType("integer")
                        .HasColumnName("ano_modelo");

                    b.Property<string>("Chassi")
                        .HasMaxLength(30)
                        .HasColumnType("character varying(30)")
                        .HasColumnName("chassi");

                    b.Property<string>("Modelo")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("modelo");

                    b.Property<string>("Placa")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("placa");

                    b.Property<string>("Renavam")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("renavam");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.ToTable("veiculos", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Documentos.NotaFiscal", b =>
                {
                    b.HasBaseType("Zin.Domain.Entidades.ZinPag.Documentos.Documento");

                    b.Property<string>("Serie")
                        .HasColumnType("text")
                        .HasColumnName("serie");

                    b.Property<decimal?>("ValorICMS")
                        .HasColumnType("numeric")
                        .HasColumnName("icms");

                    b.Property<decimal?>("ValorIPI")
                        .HasColumnType("numeric")
                        .HasColumnName("ipi");

                    b.ToTable("notas_fiscais", "zinpag");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.ClienteEmpresa", b =>
                {
                    b.HasBaseType("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaJuridica");

                    b.Property<bool>("Matriz")
                        .HasColumnType("boolean")
                        .HasColumnName("matriz");

                    b.ToTable("cliente_empresas", "cadastros");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.DadosBancarios.DadoBancario", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", null)
                        .WithMany("DadosBancarios")
                        .HasForeignKey("PessoaId");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Enderecos.Cidade", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.Cadastros.Enderecos.Estado", "Estado")
                        .WithMany("Cidades")
                        .HasForeignKey("IdEstado")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Estado");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaContato", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.Cadastros.Contatos.Contato", "Contato")
                        .WithMany()
                        .HasForeignKey("IdContato")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", "Pessoa")
                        .WithMany("PessoasContatos")
                        .HasForeignKey("IdPessoa")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contato");

                    b.Navigation("Pessoa");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaDadoBancario", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.Cadastros.DadosBancarios.DadoBancario", "DadoBancario")
                        .WithMany()
                        .HasForeignKey("IdDadoBancario")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", "Pessoa")
                        .WithMany()
                        .HasForeignKey("IdPessoa")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DadoBancario");

                    b.Navigation("Pessoa");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaEndereco", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.Cadastros.Enderecos.Endereco", "Endereco")
                        .WithMany()
                        .HasForeignKey("IdEndereco")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", "Pessoa")
                        .WithMany("PessoasEnderecos")
                        .HasForeignKey("IdPessoa")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Endereco");

                    b.Navigation("Pessoa");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Agregadores.Agregador", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.ClienteEmpresa", "ClienteEmpresa")
                        .WithMany("Agregadores")
                        .HasForeignKey("IdClienteEmpresa")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ClienteEmpresa");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Ativos.VeiculoOficina", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaJuridica", "Oficina")
                        .WithMany()
                        .HasForeignKey("OficinaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.ZinPag.Ativos.Veiculo", "Veiculo")
                        .WithMany("Oficinas")
                        .HasForeignKey("VeiculoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Oficina");

                    b.Navigation("Veiculo");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Documentos.Documento", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Agregadores.Agregador", "Agregador")
                        .WithMany("Documentos")
                        .HasForeignKey("IdAgregador")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", "Destinatario")
                        .WithMany()
                        .HasForeignKey("IdPessoaDestinatario")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", "Emitente")
                        .WithMany()
                        .HasForeignKey("IdPessoaEmitente")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Agregador");

                    b.Navigation("Destinatario");

                    b.Navigation("Emitente");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Documentos.DocumentoItemVersao", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Documentos.Documento", null)
                        .WithMany("DocumentoItemVersoes")
                        .HasForeignKey("DocumentoId");

                    b.HasOne("Zin.Domain.Entidades.ZinPag.Documentos.DocumentoPagamento", "DocumentoPagamento")
                        .WithMany()
                        .HasForeignKey("IdDocumentoPagamento")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.ZinPag.Itens.ItemVersao", "ItemVersao")
                        .WithMany()
                        .HasForeignKey("IdItemVersao")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentoPagamento");

                    b.Navigation("ItemVersao");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Documentos.DocumentoPagamento", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Documentos.Documento", "Documento")
                        .WithOne("DocumentoPagamento")
                        .HasForeignKey("Zin.Domain.Entidades.ZinPag.Documentos.DocumentoPagamento", "IdDocumento")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.ZinPag.Pagamentos.Pagamento", "Pagamento")
                        .WithMany("DocumentoPagamentos")
                        .HasForeignKey("IdPagamento")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Documento");

                    b.Navigation("Pagamento");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Itens.Item", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Agregadores.Agregador", "Agregador")
                        .WithMany("Itens")
                        .HasForeignKey("IdAgregador")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Agregador");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Itens.ItemVersao", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Documentos.Documento", "Documento")
                        .WithMany()
                        .HasForeignKey("IdDocumento");

                    b.HasOne("Zin.Domain.Entidades.ZinPag.Itens.Item", "Item")
                        .WithMany("Versoes")
                        .HasForeignKey("IdItem")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", "Oficina")
                        .WithMany()
                        .HasForeignKey("IdOficina")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", "PessoaFornecedora")
                        .WithMany()
                        .HasForeignKey("IdPessoaFornecedora")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.ZinPag.Ativos.Veiculo", "Veiculo")
                        .WithMany()
                        .HasForeignKey("IdVeiculo")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.ZinPag.Itens.ItemVersao", "VersaoAnterior")
                        .WithMany()
                        .HasForeignKey("IdVersaoAnterior");

                    b.Navigation("Documento");

                    b.Navigation("Item");

                    b.Navigation("Oficina");

                    b.Navigation("PessoaFornecedora");

                    b.Navigation("Veiculo");

                    b.Navigation("VersaoAnterior");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Pagamentos.LiquidacaoPagamento", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Pagamentos.Pagamento", "Pagamento")
                        .WithMany("LiquidacoesPagamentos")
                        .HasForeignKey("IdPagamento")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Pagamento");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Pagamentos.Pagamento", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Agregadores.Agregador", "Agregador")
                        .WithMany()
                        .HasForeignKey("IdAgregador");

                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", "Beneficiario")
                        .WithMany()
                        .HasForeignKey("IdPessoaBeneficiaria")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Agregador");

                    b.Navigation("Beneficiario");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Pagamentos.PagamentoItemVersao", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Itens.ItemVersao", "ItemVersao")
                        .WithMany()
                        .HasForeignKey("IdItemVersao")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.ZinPag.Pagamentos.Pagamento", "Pagamento")
                        .WithMany("PagamentoItemVersoes")
                        .HasForeignKey("IdPagamento")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ItemVersao");

                    b.Navigation("Pagamento");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Processamento.ProcessamentoAgregadorStatus", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Agregadores.Agregador", "Agregador")
                        .WithMany()
                        .HasForeignKey("IdAgregador")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Agregador");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Processamento.RegistroProcessamentoItemVersao", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Itens.ItemVersao", "ItemVersao")
                        .WithMany()
                        .HasForeignKey("IdItemVersao")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ItemVersao");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Ressarcimentos.Ressarcimento", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Agregadores.Agregador", "Agregador")
                        .WithMany()
                        .HasForeignKey("IdAgregador")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", "Pessoa")
                        .WithMany()
                        .HasForeignKey("IdPessoa")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Agregador");

                    b.Navigation("Pessoa");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Ressarcimentos.RessarcimentoItemVersao", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Itens.ItemVersao", "ItemVersao")
                        .WithMany()
                        .HasForeignKey("IdItemVersao")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Zin.Domain.Entidades.ZinPag.Ressarcimentos.Ressarcimento", "Ressarcimento")
                        .WithMany("RessarcimentoItemVersoes")
                        .HasForeignKey("IdRessarcimento")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ItemVersao");

                    b.Navigation("Ressarcimento");
                });

            modelBuilder.Entity("agregadores_ativos", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Agregadores.Agregador", null)
                        .WithMany()
                        .HasForeignKey("id_agregador")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_agregadores_ativos_agregadores_id_agregador");

                    b.HasOne("Zin.Domain.Entidades.ZinPag.Ativos.Ativo", null)
                        .WithMany()
                        .HasForeignKey("id_ativo")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_agregadores_ativos_ativos_id_ativo");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaFisica", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", null)
                        .WithOne()
                        .HasForeignKey("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaFisica", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaJuridica", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", null)
                        .WithOne()
                        .HasForeignKey("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaJuridica", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Ativos.Veiculo", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Ativos.Ativo", null)
                        .WithOne()
                        .HasForeignKey("Zin.Domain.Entidades.ZinPag.Ativos.Veiculo", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Documentos.NotaFiscal", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.ZinPag.Documentos.Documento", null)
                        .WithOne()
                        .HasForeignKey("Zin.Domain.Entidades.ZinPag.Documentos.NotaFiscal", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.ClienteEmpresa", b =>
                {
                    b.HasOne("Zin.Domain.Entidades.Cadastros.Pessoas.PessoaJuridica", null)
                        .WithOne()
                        .HasForeignKey("Zin.Domain.Entidades.Cadastros.Pessoas.ClienteEmpresa", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Enderecos.Estado", b =>
                {
                    b.Navigation("Cidades");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.Pessoa", b =>
                {
                    b.Navigation("DadosBancarios");

                    b.Navigation("PessoasContatos");

                    b.Navigation("PessoasEnderecos");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Agregadores.Agregador", b =>
                {
                    b.Navigation("Documentos");

                    b.Navigation("Itens");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Documentos.Documento", b =>
                {
                    b.Navigation("DocumentoItemVersoes");

                    b.Navigation("DocumentoPagamento");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Itens.Item", b =>
                {
                    b.Navigation("Versoes");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Pagamentos.Pagamento", b =>
                {
                    b.Navigation("DocumentoPagamentos");

                    b.Navigation("LiquidacoesPagamentos");

                    b.Navigation("PagamentoItemVersoes");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Ressarcimentos.Ressarcimento", b =>
                {
                    b.Navigation("RessarcimentoItemVersoes");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.ZinPag.Ativos.Veiculo", b =>
                {
                    b.Navigation("Oficinas");
                });

            modelBuilder.Entity("Zin.Domain.Entidades.Cadastros.Pessoas.ClienteEmpresa", b =>
                {
                    b.Navigation("Agregadores");
                });
#pragma warning restore 612, 618
        }
    }
}
