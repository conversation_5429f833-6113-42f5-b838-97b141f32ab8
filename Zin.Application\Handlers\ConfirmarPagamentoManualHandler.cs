﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Application.Services.ZinPag;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Enums;

namespace Zin.Application.Handlers
{
    public class ConfirmarPagamentoManualHandler
    {
        public async Task<PagamentoManualResult> HandleAsync(ConfirmarPagamentoManualCommand command)
        {
            var pagamento = await command.PagamentoRepository.BuscarPorIdAsync(command.PagamentoId);

            if (pagamento == null)
                return new PagamentoManualResult { Sucesso = false, Mensagem = "Pagamento não encontrado." };
            if (pagamento.Cancelado)
                return new PagamentoManualResult { Sucesso = false, Mensagem = "Pagamento cancelado." };
            if (pagamento.StatusPagamento == StatusPagamento.Pago)
                return new PagamentoManualResult { Sucesso = false, Mensagem = "Pagamento já está pago." };

            pagamento.StatusPagamento = StatusPagamento.Pago;
            pagamento.DataAtualizacao = DateTime.UtcNow;

            await command.PagamentoRepository.AtualizarAsync(pagamento);

            var liquidacao = new LiquidacaoPagamento
            {
                IdPagamento = pagamento.Id,
                Data = DateTime.UtcNow,
                StatusPagamento = StatusPagamento.Pago,
                ValorPago = pagamento.Valor
            };

            await command.LiquidacaoPagamentoRepository.InserirAsync(liquidacao);
            return new PagamentoManualResult { Sucesso = true, Mensagem = "Pagamento confirmado com sucesso." };
        }
    }
}
