﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Zin.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AlteraCamposItemVersaoEDocumento : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_itens_versoes_veiculos_id_veiculo",
                schema: "zinpag",
                table: "itens_versoes");

            migrationBuilder.RenameColumn(
                name: "id_veiculo",
                schema: "zinpag",
                table: "itens_versoes",
                newName: "id_ativo");

            migrationBuilder.RenameIndex(
                name: "IX_itens_versoes_id_veiculo",
                schema: "zinpag",
                table: "itens_versoes",
                newName: "IX_itens_versoes_id_ativo");

            migrationBuilder.AddColumn<int>(
                name: "status_documento",
                schema: "zinpag",
                table: "documentos",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddForeignKey(
                name: "FK_itens_versoes_ativos_id_ativo",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_ativo",
                principalSchema: "zinpag",
                principalTable: "ativos",
                principalColumn: "id_ativo",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_itens_versoes_ativos_id_ativo",
                schema: "zinpag",
                table: "itens_versoes");

            migrationBuilder.DropColumn(
                name: "status_documento",
                schema: "zinpag",
                table: "documentos");

            migrationBuilder.RenameColumn(
                name: "id_ativo",
                schema: "zinpag",
                table: "itens_versoes",
                newName: "id_veiculo");

            migrationBuilder.RenameIndex(
                name: "IX_itens_versoes_id_ativo",
                schema: "zinpag",
                table: "itens_versoes",
                newName: "IX_itens_versoes_id_veiculo");

            migrationBuilder.AddForeignKey(
                name: "FK_itens_versoes_veiculos_id_veiculo",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_veiculo",
                principalSchema: "zinpag",
                principalTable: "veiculos",
                principalColumn: "id_ativo",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
