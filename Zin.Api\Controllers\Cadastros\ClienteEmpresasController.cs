﻿using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Empresas;
using Zin.Application.Services.Cadastros.Interfaces;

namespace Zin.Api.Controllers.Cadastros
{
    [ApiController]
    [Route("cliente-empresas")]
    public class ClienteEmpresasController(IClienteEmpresaService clienteEmpresaService) : ControllerBase
    {
        private readonly IClienteEmpresaService _clienteEmpresaService = clienteEmpresaService;

        [HttpPost]
        public async Task<IActionResult> CriarClienteEmpresa([FromBody] CriaClienteEmpresaDto criaEmpresaDto)
        {
            var id = await _clienteEmpresaService.CriaClienteEmpresaAsync(criaEmpresaDto);
            return CreatedAtAction(nameof(CriarClienteEmpresa), new { id }, id);
        }        
    }
}
