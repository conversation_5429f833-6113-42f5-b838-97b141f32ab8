﻿using AutoMapper;
using Zin.Application.DTOs.Ressarcimentos;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Entidades.ZinPag.Ressarcimentos;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class RessarcimentoService(
        IRessarcimentoRepository ressarcimentoRepository,
        IMapper mapper,
        IPagamentoRepository pagamentoRepository) : IRessarcimentoService
    {

        private readonly IRessarcimentoRepository _ressarcimentoRepository = ressarcimentoRepository;
        private readonly IMapper _mapper = mapper;
        private readonly IPagamentoRepository _pagamentoRepository = pagamentoRepository;

        public Task AtualizaRessarcimentoAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task AtualizaStatusRessarcimento(int id)
        {
            throw new NotImplementedException();
        }

        public async Task<int> CriarRessarcimentoAsync(CriaRessarcimentoDto dto)
        {
            if (dto.IdAgregador == 0)
                throw new ArgumentNullException(nameof(dto.IdAgregador), "Id do agregador não pode ser nulo.");

            if (dto.IdPessoa == 0)
                throw new ArgumentNullException(nameof(dto.IdPessoa), "Id da pessoa não pode ser nulo.");

            var pagamentosEmAberto = await _pagamentoRepository.BuscarAsync(r =>
                r.IdAgregador == dto.IdAgregador 
                && r.PagamentoItemVersoes.Any(iv => dto.ItensVersoesIds.Contains(iv.Id))
                && (r.StatusPagamento != StatusPagamento.Pago)
            );

            if (pagamentosEmAberto.Any())
                throw new InvalidOperationException("Já existe um pagamento não finalizado para este agregador.");

            Ressarcimento ressarcimento = new Ressarcimento
            {
                IdAgregador = dto.IdAgregador,
                IdPessoa = dto.IdPessoa,
                Valor = dto.Valor,
                DataSolicitacao = DateTime.UtcNow,
                Cancelado = false,
                StatusRessarcimento = StatusRessarcimento.Pendente
            };

            if (dto.ItensVersoesIds != null && dto.ItensVersoesIds.Any())
            {
                List<RessarcimentoItemVersao> ressarcimentoItemVersoes = new List<RessarcimentoItemVersao>();

                foreach (var itemVersaoId in dto.ItensVersoesIds)
                {
                    // Verifica se já existe pagamento PAGO para este item
                    var itemPago = await _pagamentoRepository.BuscarAsync(r =>
                        r.IdAgregador == dto.IdAgregador &&
                        r.StatusPagamento == StatusPagamento.Pago &&
                        r.PagamentoItemVersoes.Any(piv => piv.IdItemVersao == itemVersaoId)
                    );

                    if (itemPago.Any())
                    {
                        throw new InvalidOperationException($"O item {itemVersaoId} já possui um documento pago.");
                    }

                    var ressarcimentoItemVersao = new RessarcimentoItemVersao
                    {
                        IdItemVersao = itemVersaoId,
                        Ressarcimento = ressarcimento
                    };

                    ressarcimentoItemVersoes.Add(ressarcimentoItemVersao);             
                    
                    var itemVersaoEmPagamento = await _pagamentoRepository.BuscarAsync(r =>
                       r.IdAgregador == dto.IdAgregador &&
                       r.PagamentoItemVersoes.Any(piv => dto.ItensVersoesIds.Contains(piv.IdItemVersao))
                    );

                    if (itemVersaoEmPagamento.Any())
                    {
                        throw new InvalidOperationException("Este Item já está em processo de pagamento");
                    }
                }

                ressarcimento.RessarcimentoItemVersoes = ressarcimentoItemVersoes;
            }


            return await _ressarcimentoRepository.InserirAsync(ressarcimento);
        }
        
        public Task<bool> CancelaRessarcimentoAsync(int id)
        {
            throw new NotImplementedException();
        }
    }
}
