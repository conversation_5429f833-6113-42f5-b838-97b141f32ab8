﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Zin.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class EntidadeMovimentacoes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "id_movimentacao",
                schema: "zinpag",
                table: "itens_versoes",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "movimentacoes",
                schema: "zinpag",
                columns: table => new
                {
                    id_movimentacao = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    id_agregador = table.Column<int>(type: "integer", nullable: false),
                    id_documento = table.Column<int>(type: "integer", nullable: true),
                    data_hora_autorizacao = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_movimentacoes", x => x.id_movimentacao);
                    table.ForeignKey(
                        name: "FK_movimentacoes_agregadores_id_agregador",
                        column: x => x.id_agregador,
                        principalSchema: "zinpag",
                        principalTable: "agregadores",
                        principalColumn: "id_agregador",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_movimentacoes_documentos_id_documento",
                        column: x => x.id_documento,
                        principalSchema: "zinpag",
                        principalTable: "documentos",
                        principalColumn: "id_documento");
                });

            migrationBuilder.CreateIndex(
                name: "IX_itens_versoes_id_movimentacao",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_movimentacao");

            migrationBuilder.CreateIndex(
                name: "IX_movimentacoes_id_agregador",
                schema: "zinpag",
                table: "movimentacoes",
                column: "id_agregador");

            migrationBuilder.CreateIndex(
                name: "IX_movimentacoes_id_documento",
                schema: "zinpag",
                table: "movimentacoes",
                column: "id_documento");

            migrationBuilder.AddForeignKey(
                name: "FK_itens_versoes_movimentacoes_id_movimentacao",
                schema: "zinpag",
                table: "itens_versoes",
                column: "id_movimentacao",
                principalSchema: "zinpag",
                principalTable: "movimentacoes",
                principalColumn: "id_movimentacao");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_itens_versoes_movimentacoes_id_movimentacao",
                schema: "zinpag",
                table: "itens_versoes");

            migrationBuilder.DropTable(
                name: "movimentacoes",
                schema: "zinpag");

            migrationBuilder.DropIndex(
                name: "IX_itens_versoes_id_movimentacao",
                schema: "zinpag",
                table: "itens_versoes");

            migrationBuilder.DropColumn(
                name: "id_movimentacao",
                schema: "zinpag",
                table: "itens_versoes");
        }
    }
}
