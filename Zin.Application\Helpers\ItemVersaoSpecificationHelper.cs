﻿using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Application.Helpers
{
    public static class ItemVersaoSpecificationHelper
    {
        public static DeveCriarNovaVersaoResponse GeraDeveCriarNovaVersaoResponse(bool deveCriarNovaVersao, ItemVersao? versaoAnterior)
        {
            return new DeveCriarNovaVersaoResponse
            {
                DeveCriarNovaVersao = deveCriarNovaVersao,
                VersaoAnterior = versaoAnterior
            };
        }

        public static string MensagemErroExclusaoSemAutorizacao(ImportarItensDTO itemDto)
        {
            return $"Não é possível criar uma versão de exclusão do item {itemDto.Codigo} " +
                   $"sem uma versão de autorização anterior para o fornecedor {itemDto.CnpjFornecedor} " +
                   $"com a data de autorização {itemDto.DataAutorizacao:dd/MM/yyyy}.";
        }

        public static IEnumerable<ItemVersao>? ObterTodasVersoesMesmoFornecedorEAutorizacao(
            ICollection<ItemVersao> todasVersoes,
            DateTime dataAutorizacao,
            string cnpjFornecedor)
        {
            return todasVersoes
                .Where(v => v.DataHoraAutorizacao == dataAutorizacao
                    && v.PessoaFornecedora is PessoaJuridica pj && pj.Cnpj == cnpjFornecedor);
        }

        public static ItemVersao? ObterUltimaVersaoMesmaAutorizacao(
            ICollection<ItemVersao> todasVersoes,
            DateTime dataAutorizacao,
            string cnpjFornecedor)
        {
            return todasVersoes
                .Where(v => v.DataHoraAutorizacao == dataAutorizacao
                            && (v.PessoaFornecedora as PessoaJuridica)!.Cnpj == cnpjFornecedor)
                .OrderByDescending(v => v.DataCriacao)
                .FirstOrDefault();
        }
    }
}
