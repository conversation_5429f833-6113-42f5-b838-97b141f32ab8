﻿using Microsoft.Extensions.Logging;
using Zin.Application.Helpers;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.Processos
{
    public class PossivelRessarcimentoService : IPossivelRessarcimentoService
    {
        private readonly IItemVersaoRepository _itemVersaoRepositorio;
        private readonly IPagamentoRepository _pagamentoRepositorio;
        private readonly IRessarcimentoRepository _ressarcimentoRepositorio;
        private readonly IRegistroProcessamentoRepository _registroProcessamentoRepositorio;
        private readonly IItemRepository _itemRepositorio;
        private readonly ILogger<PossivelRessarcimentoService> _logger;
        private readonly ICondicaoComparadorService _condicaoComparadorService;
        private readonly IConfiguracaoService _configuracaoService;

        public PossivelRessarcimentoService(
            IItemVersaoRepository itemVersaoRepositorio,
            IPagamentoRepository pagamentoRepositorio,
            IRessarcimentoRepository ressarcimentoRepositorio,
            IRegistroProcessamentoRepository registroProcessamentoRepositorio,
            IItemRepository itemRepositorio,
            IAgregadorRepository agregadorRepositorio,
            ICondicaoComparadorService condicaoComparadorService,
            IConfiguracaoService configuracaoService,
            ILogger<PossivelRessarcimentoService> logger)
        {
            _itemVersaoRepositorio = itemVersaoRepositorio;
            _pagamentoRepositorio = pagamentoRepositorio;
            _ressarcimentoRepositorio = ressarcimentoRepositorio;
            _registroProcessamentoRepositorio = registroProcessamentoRepositorio;
            _itemRepositorio = itemRepositorio;
            _logger = logger;
            _condicaoComparadorService = condicaoComparadorService;
            _configuracaoService = configuracaoService;

        }

        public async Task ProcessarAsync(int idAgregador)
        {
            var itensVersao = await _itemVersaoRepositorio.BuscarPorAgregadorIdAsync(idAgregador);

            foreach (var itemVersao in itensVersao)
            {
                try
                {
                    // Só considera exclusões
                    if (itemVersao.TipoMovimento != TipoMovimentoItem.Exclusao)
                        continue;

                    // Busca a autorização correspondente pela DataHoraAutorizacao
                    var autorizacao = itensVersao.FirstOrDefault(v =>
                        v.TipoMovimento == TipoMovimentoItem.Autorizacao &&
                        v.DataHoraAutorizacao == itemVersao.DataHoraAutorizacao
                    );
                    if (autorizacao == null)
                    {
                        await RegistroProcessamentoHelper.RegistrarAsync(
                            itemVersao,
                            TipoProcessamento.Ressarcimento,
                            StatusProcessamento.Erro,
                            Divergencia.SemDivergencia,
                            Condicao.SemCondicao,
                            "Não há autorização correspondente para essa exclusão.",
                            _registroProcessamentoRepositorio,
                            _itemVersaoRepositorio
                        );
                        await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
                        continue;
                    }

                    // Tem que ter pagamento efetuado para a autorização
                    var pagamentos = await _pagamentoRepositorio.BuscarPagamentosPorItemVersaoIdAsync(autorizacao.Id);
                    if (!pagamentos.Any())
                    {
                        await RegistroProcessamentoHelper.RegistrarAsync(
                            itemVersao,
                            TipoProcessamento.Ressarcimento,
                            StatusProcessamento.Processado,
                            Divergencia.SemDivergencia,
                            Condicao.SemCondicao,
                            "Não há pagamento para a autorização correspondente.",
                            _registroProcessamentoRepositorio,
                            _itemVersaoRepositorio
                        );
                        await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
                        continue;
                    }

                    // Não pode já ter ressarcimento
                    var ressarcimentos = await _ressarcimentoRepositorio.BuscarPorItemVersaoIdAsync(autorizacao.Id);
                    if (ressarcimentos.Any())
                    {
                        await RegistroProcessamentoHelper.RegistrarAsync(
                            itemVersao,
                            TipoProcessamento.Ressarcimento,
                            StatusProcessamento.RessarcimentoEfetuado,
                            Divergencia.SemDivergencia,
                            Condicao.SemCondicao,
                            "Ressarcimento já realizado para esta autorização.",
                            _registroProcessamentoRepositorio,
                            _itemVersaoRepositorio
                        );
                        await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
                        continue;
                    }

                    // Verificação dinâmica das regras da configuração
                    var configuracoes = await _configuracaoService.BuscarPorTipoProcessamentoAsync(TipoProcessamento.Ressarcimento);
                    var apto = true;
                    foreach (var config in configuracoes)
                    {
                        if (config == null)
                        {
                            // Configuração padrão, não deve ser processada
                            continue;
                        }
                        else
                        {
                            var resultadosRegras = await _condicaoComparadorService.VerificaAsync(config.Id, itemVersao);
                            if (!resultadosRegras.Resultado)
                            {
                                apto = false;
                                Condicao condicaoRegra = Condicao.SemCondicao;
                                switch (resultadosRegras.TipoConfiguracao)
                                {
                                    case "QuantidadeDiasAposPrazoEntrega":
                                        condicaoRegra = Condicao.MenosDe35DiasEntrega;
                                        break;
                                    case "SituacaoVeiculo":
                                        condicaoRegra = Condicao.VeiculoNaoEntregue;
                                        break;
                                    case "SituacaoNotaFiscalVenda":
                                        condicaoRegra = Condicao.NotaFiscalNaoAnexada;
                                        break;
                                }

                                await RegistroProcessamentoHelper.RegistrarAsync(
                                    itemVersao,
                                    TipoProcessamento.Ressarcimento,
                                    StatusProcessamento.PossivelRessarcimento,
                                    Divergencia.SemDivergencia,
                                    condicaoRegra,
                                    $"Não conforme na regra {resultadosRegras.NomeRegra} do configuração {resultadosRegras.TipoConfiguracao}",
                                    null,
                                    _itemVersaoRepositorio
                                );
                                break;
                            }
                        }
                    }

                    // Se passou por todas as condições, é possível pagamento
                    if (!apto)
                    {
                        await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
                        continue;
                    }

                    // Caso apto para ressarcimento
                    await RegistroProcessamentoHelper.RegistrarAsync(
                        itemVersao,
                        TipoProcessamento.Ressarcimento,
                        StatusProcessamento.PossivelRessarcimento,
                        Divergencia.SemDivergencia,
                        Condicao.AprovadoRessarcimento,
                        $"ItemVersao apto para ressarcimento. Valor: {autorizacao.ValorTotal}, Item: {autorizacao.Item?.Id}",
                        _registroProcessamentoRepositorio,
                        _itemVersaoRepositorio
                    );
                }
                catch (Exception ex)
                {
                    StatusAtualizador.SetStatusProcessamentoItemVersao(itemVersao, TipoProcessamento.Ressarcimento, StatusProcessamento.Erro);
                    await _itemVersaoRepositorio.AtualizarAsync(itemVersao);

                    if (itemVersao.Item != null)
                    {
                        itemVersao.Item.StatusProcessamento = StatusProcessamento.Erro;
                        await _itemRepositorio.AtualizarAsync(itemVersao.Item);
                    }
                    _logger.LogError(ex, $"Erro ao processar versão {itemVersao.Id} do item {itemVersao.IdItem} no agregador {idAgregador}");
                }
                await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(itemVersao.IdItem, _itemRepositorio, _itemVersaoRepositorio);
            }
        }
    }
}