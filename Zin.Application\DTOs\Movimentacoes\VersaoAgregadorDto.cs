namespace Zin.Application.DTOs.Movimentacoes
{
    public class VersaoAgregadorDto
    {
        public int IdItemVersao { get; set; }
        public FornecedorAgregadorDto Fornecedor { get; set; } = new();
        public string TipoMovimento { get; set; } = string.Empty;
        public DateTime? DataAutorizacao { get; set; }
        public List<DocumentoAgregadorDto> Documentos { get; set; } = [];
        public int Quantidade { get; set; }
        public decimal ValorTotal { get; set; }
        public DateTime? DataEntrega { get; set; }
        public List<DivergenciaAgregadorDto> Divergencias { get; set; } = [];
    }
}
