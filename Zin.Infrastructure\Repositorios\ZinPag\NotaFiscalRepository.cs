﻿using Microsoft.EntityFrameworkCore;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class NotaFiscalRepository(IUnitOfWork unitOfWork) : INotafiscalRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;

        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(NotaFiscal entidade)
        {
            Context.NotasFiscais.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<NotaFiscal>> BuscarAsync(System.Linq.Expressions.Expression<Func<NotaFiscal, bool>> predicado)
        {
            return await Context.NotasFiscais.Where(predicado).ToListAsync();
        }

        public async Task<NotaFiscal?> BuscarPorIdAsync(int id)
        {
            return await Context.NotasFiscais.FindAsync(id)
                .AsTask() ?? throw new Exception($"Nota Fiscal com ID {id} não encontrada.");
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<NotaFiscal> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<int> InserirAsync(NotaFiscal entidade)
        {
            await Context.NotasFiscais.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<NotaFiscal> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<IEnumerable<NotaFiscal>> ListarAsync()
        {
            return await Context.NotasFiscais.ToListAsync();
        }



    }
}
