﻿using Zin.Application.DTOs.Movimentacoes;

namespace Zin.Application.Services.ZinPag.Interfaces
{
    public interface IMovimentacoesService
    {
        Task<ObterMovimentacaoDetalhesDto?> ObterDetalhesMovimentacaoAsync(int id);
        Task<List<MovimentacaoDto>> ListarMovimentacoesAsync();
        Task<MovimentacaoDetalhesDto> DetalheMovimentacao(int idMovimentacao);
        Task<List<ItemAgregadorDto>> ObterItensOutrasMovimentacoesAsync(int idAgregador, int movId);
    }
}
