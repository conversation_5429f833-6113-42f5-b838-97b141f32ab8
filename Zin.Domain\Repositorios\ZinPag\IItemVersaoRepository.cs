﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface IItemVersaoRepository
    {
        Task<int> InserirAsync(ItemVersao itemVersao);
        Task AtualizarAsync(ItemVersao itemVersao);
        Task<ItemVersao?> BuscarPorIdAsync(int id);
        Task<IEnumerable<ItemVersao>> ListarAsync();
        Task<IEnumerable<ItemVersao>> BuscarAsync(Expression<Func<ItemVersao, bool>> predicate);
        Task DeletarAsync(int id);
        Task<IEnumerable<ItemVersao>> BuscarPorItemIdAsync(int idItem);
        Task<IEnumerable<ItemVersao>> BuscarPorAgregadorIdAsync(int idAgregador);
    }
}
