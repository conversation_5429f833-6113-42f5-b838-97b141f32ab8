﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Application.DTOs.Itens;

namespace Zin.Application.Services.ZinPag.Interfaces
{
    public interface IItemService
    {
        Task<ItemDto> CriarItemAsync(CriaItemDto dto);
        Task<IEnumerable<ItemDto>> ListarItensPorAgregadorAsync(int idAgregador);
        Task<ItemDto?> ObterItemPorIdAsync(int id);
    }
}
