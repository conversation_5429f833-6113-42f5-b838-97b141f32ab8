using Microsoft.AspNetCore.Mvc;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Api.Controllers.ZinPag
{
    [ApiController]
    [Route("documentos")]
    public class DocumentosController : ControllerBase
    {
        private readonly IDocumentoService _documentoService;

        public DocumentosController(IDocumentoService documentoService)
        {
            _documentoService = documentoService;
        }

        [HttpGet("{documentoId}/download")]
        public async Task<IActionResult> DownloadDocumento(int documentoId)
        {
            try
            {
                var result = await _documentoService.DownloadDocumentoAsync(documentoId);
                return File(result.FileBytes, result.ContentType, result.FileName);
            }
            catch (FileNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro interno: {ex.Message}");
            }
        }
    }
}
