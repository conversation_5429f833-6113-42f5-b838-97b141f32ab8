using System.Security.Claims;

namespace Zin.Helpers.Clientes
{
    public static class ClienteClaimsHelper
    {
        public static string? ExtrairNomeBaseDeDadosDoClienteDeClaims(ClaimsPrincipal user)
        {
            return user.Claims.FirstOrDefault(c => c.Type == "cliente_base_dados")?.Value;
        }

        public static string? ExtrairCnpjBaseDeDadosDoClienteDeClaims(ClaimsPrincipal user)
        {
            return user.Claims.FirstOrDefault(c => c.Type == "cliente_cnpj")?.Value;
        }
    }
}
