using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Ativos;

namespace Zin.Domain.Entidades.ZinPag.Ativos
{
    [Table("veiculo_oficina", Schema = "zinpag")]
    public class VeiculoOficina
    {
        [Key]
        [Column("id_veiculo_oficina")]
        public int Id { get; set; }

        [Column("id_veiculo")]
        public int VeiculoId { get; set; }

        [Column("id_pessoa_oficina")]
        public int OficinaId { get; set; }

        [Column("data_entrada")]
        public DateTime? DataEntrada { get; set; }

        [Column("data_saida")]
        public DateTime? DataSaida { get; set; }

        [Column("veiculo_presente")]
        public bool VeiculoPresente { get; set; }

        [ForeignKey(nameof(VeiculoId))]
        public Veiculo Veiculo { get; set; } = null!;

        [ForeignKey(nameof(OficinaId))]
        public PessoaJuridica Oficina { get; set; } = null!;
    }
}
