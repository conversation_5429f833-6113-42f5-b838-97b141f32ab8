using AutoMapper;
using Moq;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Services.ZinPag;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Tests.Itens
{
    public class ItemVersaoServiceTests
    {
        private readonly Mock<IItemVersaoRepository> _repoMock = new();
        private readonly Mock<IMapper> _mapperMock = new();

        private ItemVersaoService CriarService()
        {
            var service = new ItemVersaoService(_repoMock.Object, _mapperMock.Object);
            return service;
        }

        [Fact]
        public void DeveCriarNovaVersao_ItemSemVersoes_RetornaTrue()
        {
            var service = CriarService();
            PessoaJuridica fornecedor = GeraFornecedorTeste();
            var todasVersoes = new List<ItemVersao>();
            var dto = new ImportarItensDTO() { TipoMovimentoItem = TipoMovimentoItem.Autorizacao };

            Assert.True(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_ExclusaoComAutorizacao_RetornaTrue()
        {
            var service = CriarService();
            PessoaJuridica fornecedor = GeraFornecedorTeste();
            var todasVersoes = new List<ItemVersao> { new() { DataHoraAutorizacao = DateTime.Today, TipoMovimento = TipoMovimentoItem.Autorizacao, PessoaFornecedora = fornecedor } };
            var dto = new ImportarItensDTO { DataAutorizacao = DateTime.Today, TipoMovimentoItem = TipoMovimentoItem.Exclusao, CnpjFornecedor = fornecedor.Cnpj };

            Assert.True(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_ExclusaoSemAutorizacao_RetornaExcecao()
        {
            var service = CriarService();
            var todasVersoes = new List<ItemVersao>();
            var dto = new ImportarItensDTO { TipoMovimentoItem = TipoMovimentoItem.Exclusao };

            Assert.Throws<InvalidOperationException>(() => service.DeveCriarNovaVersao(todasVersoes, dto));
        }

        [Fact]
        public void DeveCriarNovaVersao_DataAutorizacaoNovaMesmoFornecedor_RetornaTrue()
        {
            var service = CriarService();
            PessoaJuridica fornecedor = GeraFornecedorTeste();
            var todasVersoes = new List<ItemVersao> { new() { DataHoraAutorizacao = DateTime.Today.AddDays(-1), PessoaFornecedora = fornecedor } };
            var dto = new ImportarItensDTO { TipoMovimentoItem = TipoMovimentoItem.Autorizacao, DataAutorizacao = DateTime.Today, CnpjFornecedor = fornecedor.Cnpj };

            Assert.True(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_DataAutorizacaoIgualMesmoFornecedor_RetornaFalse()
        {
            var service = CriarService();
            PessoaJuridica fornecedor = GeraFornecedorTeste();
            var todasVersoes = new List<ItemVersao> { new() { DataHoraAutorizacao = DateTime.Today, PessoaFornecedora = fornecedor } };
            var dto = new ImportarItensDTO { TipoMovimentoItem = TipoMovimentoItem.Autorizacao, DataAutorizacao = DateTime.Today, CnpjFornecedor = fornecedor.Cnpj };

            Assert.False(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_DataMovimentacaoNovaDataAutorizacaoIgual_RetornaTrue()
        {
            var service = CriarService();
            PessoaJuridica fornecedor = GeraFornecedorTeste();
            var todasVersoes = new List<ItemVersao> { new() { DataHoraAutorizacao = DateTime.Today, DataHoraMovimentacao = DateTime.Today.AddDays(-1), PessoaFornecedora = fornecedor } };
            var dto = new ImportarItensDTO { DataAutorizacao = DateTime.Today, DataMovimento = DateTime.Today, CnpjFornecedor = fornecedor.Cnpj };

            Assert.True(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_DataMovimentacaoEDataAutorizacaoIgual_RetornaFalse()
        {
            var service = CriarService();
            PessoaJuridica fornecedor = GeraFornecedorTeste();
            var todasVersoes = new List<ItemVersao>
            {
                new()
                {
                    DataHoraAutorizacao = DateTime.Today,
                    DataHoraMovimentacao = DateTime.Today,
                    PessoaFornecedora = fornecedor
                }
            };
            var dto = new ImportarItensDTO
            {
                DataAutorizacao = DateTime.Today,
                DataMovimento = DateTime.Today,
                CnpjFornecedor = fornecedor.Cnpj
            };

            Assert.False(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_MesmaDataAutorizacaoQuantidadeDiferente_RetornaTrue()
        {
            var service = CriarService();
            PessoaJuridica fornecedor = GeraFornecedorTeste();
            var todasVersoes = new List<ItemVersao> { new() { DataHoraAutorizacao = DateTime.Today, Quantidade = 5, PessoaFornecedora = fornecedor, TipoMovimento = TipoMovimentoItem.Autorizacao } };
            var dto = new ImportarItensDTO { DataAutorizacao = DateTime.Today, Quantidade = 10, CnpjFornecedor = fornecedor.Cnpj, TipoMovimentoItem = TipoMovimentoItem.Autorizacao };
            Assert.True(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        [Fact]
        public void DeveCriarNovaVersao_MesmaDataAutorizacaoQuantidadeIgual_RetornaFalse()
        {
            var service = CriarService();
            PessoaJuridica fornecedor = GeraFornecedorTeste();
            var todasVersoes = new List<ItemVersao> { new() { DataHoraAutorizacao = DateTime.Today, Quantidade = 5, PessoaFornecedora = fornecedor } };
            var dto = new ImportarItensDTO { DataAutorizacao = DateTime.Today, Quantidade = 5, CnpjFornecedor = fornecedor.Cnpj };
            Assert.False(service.DeveCriarNovaVersao(todasVersoes, dto).DeveCriarNovaVersao);
        }

        private static PessoaJuridica GeraFornecedorTeste()
        {
            return new PessoaJuridica
            {
                Cnpj = "12345678901234",
                NomeFantasia = "Fornecedor Teste",
                RazaoSocial = "Fornecedor Teste"
            };
        }
    }
}