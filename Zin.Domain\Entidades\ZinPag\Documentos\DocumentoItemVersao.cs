﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Pagamentos;

namespace Zin.Domain.Entidades.ZinPag.Documentos
{
    [Table("documento_item_versao", Schema = "zinpag")]
    public class DocumentoItemVersao 
    {
        [Key]
        [Column("id_documento_item_versao")]
        public int Id { get; set; }

        [Column("id_documento_pagamento")]
        public int IdDocumentoPagamento { get; set; }

        [Column("id_item_versao")]
        public int IdItemVersao { get; set; }

        [ForeignKey(nameof(IdDocumentoPagamento))]
        public DocumentoPagamento? DocumentoPagamento { get; set; }

        [ForeignKey(nameof(IdItemVersao))]
        public ItemVersao? ItemVersao { get; set; }

    }
}
