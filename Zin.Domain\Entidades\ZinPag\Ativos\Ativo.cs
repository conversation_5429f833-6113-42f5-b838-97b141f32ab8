﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Ativos
{

    [Table("ativos", Schema = "zinpag")]
    public class Ativo
    {
        [Key]
        [Column("id_ativo")]
        public int Id { get; set; }

        [Column("tipo")]
        public TipoAtivo Tipo { get; set; }

        public ICollection<Agregador> Agregadores { get; set; } = [];
    }
}
