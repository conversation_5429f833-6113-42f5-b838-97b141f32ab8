﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Ressarcimentos
{
    [Table("ressarcimentos", Schema = "zinpag")]
    public class Ressarcimento
    {
        //1. <PERSON>ve prim<PERSON>
        [Key]
        [Column("id_ressarcimento")]
        public int Id { get; set; }

        //2. <PERSON>ves estrangeiras
        [Column("id_pessoa")]
        public int IdPessoa { get; set; } 

        [Column("id_agregador")]
        public int IdAgregador { get; set; }

        //3. Propriedades simples

        [Column("valor", TypeName = "decimal(12, 2)")]
        public decimal Valor { get; set; }

        [Column("data_solicitacao")]
        public DateTime DataSolicitacao { get; set; }

        [Column("data_pagamento")]
        public DateTime? DataPagamento { get; set; }

        [Column("forma_pagamento_ressarcimento")]
        public FormasPagamentoRessarcimento FormaPagamentoRessarcimento { get; set; } 

        [Column("status_ressarcimento")]
        public StatusRessarcimento StatusRessarcimento { get; set; }

        [Column("data_cancelamento")]
        public DateTime? DataCancelamento { get; set; }

        [Column("descricao")]
        public string? Descricao { get; set; }

        [Column("cancelado")]
        public bool Cancelado { get; set; }

        // Relacionamentos
        [ForeignKey(nameof(IdPessoa))]
        public Pessoa? Pessoa { get; set; } 
        [ForeignKey(nameof(IdAgregador))]
        public Agregador? Agregador { get; set; }

        public ICollection<RessarcimentoItemVersao> RessarcimentoItemVersoes { get; set; } = [];
    }
}
