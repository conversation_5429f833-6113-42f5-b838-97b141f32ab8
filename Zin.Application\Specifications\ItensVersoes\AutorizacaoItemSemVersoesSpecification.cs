﻿using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Application.Helpers;
using Zin.Application.Specifications.ItensVersoes.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Application.Specifications.ItensVersoes
{
    /// <summary>
    /// Specification para verificar se um item não possui versões anteriores
    /// caso não haja versões, deve ser criada uma nova versão do item.
    /// Se houver versões, mas nenhuma com a mesma data de autorização e fornecedor,
    /// também deve ser criada uma nova versão.
    /// </summary>
    public class AutorizacaoItemSemVersoesSpecification : IItemVersaoSpecification
    {
        /// <summary>
        /// Verifica se deve ser criada uma nova versão do item
        /// </summary>
        /// <param name="todasVersoes">Todas as versões deum item especifico</param>
        /// <param name="itemDto"></param>
        /// <returns></returns>
        public DeveCriarNovaVersaoResponse IsSatisfiedBy(ICollection<ItemVersao> todasVersoes, ImportarItensDTO itemDto)
        {
            bool deveCriarNovaVersao = false;

            // Versão anterior do item constante nula,
            // pois nessa Specification só se deve criar nova versão se não houver versões anteriores
            const ItemVersao? versaoAnterior = null;

            if (itemDto.TipoMovimentoItem == Domain.Enums.TipoMovimentoItem.Autorizacao)
            {

                if (todasVersoes == null
                    || todasVersoes.Count <= 0)
                {
                    // Não há versões anteriores do item, criar nova versão
                    deveCriarNovaVersao = true; 
                }
                // Não precisa buscar relacionado com a data de autorização
                var todasVersoesMesmoFornecedorEAutorizacao = ItemVersaoSpecificationHelper
                    .ObterTodasVersoesMesmoFornecedorEAutorizacao(
                        todasVersoes,
                        itemDto.DataAutorizacao,
                        itemDto.CnpjFornecedor);

                if (todasVersoesMesmoFornecedorEAutorizacao == null
                    || !todasVersoesMesmoFornecedorEAutorizacao.Any())
                {
                    // Não há versões anteriores do item com a mesma autorização e fornecedor,
                    // criar nova versão
                    deveCriarNovaVersao = true;
                }
            }

            return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(deveCriarNovaVersao, versaoAnterior);
        }
    }
}
