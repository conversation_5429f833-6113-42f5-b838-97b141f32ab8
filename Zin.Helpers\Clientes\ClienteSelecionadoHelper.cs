﻿using Microsoft.AspNetCore.Http;
using Zin.Helpers.Clientes.Models;

namespace Zin.Helpers.Clientes
{
    public static class ClienteSelecionadoHelper
    {
        public static Cliente BuscarClienteSelecionado(HttpContext context)
        {
            if (context.Items.TryGetValue("ClienteSelecionado", out var clienteObj) && clienteObj is Cliente cliente)
            {
                return cliente;
            }

            throw new Exception("Cliente selecionado não encontrado no contexto HTTP. Certifique-se de que o middleware ClienteFromClaimMiddleware foi executado corretamente.");
        }
    }
}
