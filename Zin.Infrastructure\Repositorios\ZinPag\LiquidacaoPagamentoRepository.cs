using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class LiquidacaoPagamentoRepository(IUnitOfWork unitOfWork) : ILiquidacaoPagamentoRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;

        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(LiquidacaoPagamento entidade)
        {
            Context.LiquidacoesPagamentos.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<LiquidacaoPagamento>> BuscarAsync(Expression<Func<LiquidacaoPagamento, bool>> predicado)
        {
            return await Context.LiquidacoesPagamentos.Where(predicado).ToListAsync();
        }

        public async Task<LiquidacaoPagamento> BuscarPorIdAsync(int id)
        {
            return await Context.LiquidacoesPagamentos.FindAsync(id)
                .AsTask() ?? throw new Exception($"LiquidacaoPagamento com ID {id} não encontrado.");
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<LiquidacaoPagamento> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<int> InserirAsync(LiquidacaoPagamento entidade)
        {
            await Context.LiquidacoesPagamentos.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<LiquidacaoPagamento> entidades)
        {
            throw new NotImplementedException();
        }

        public async Task<IEnumerable<LiquidacaoPagamento>> ListarAsync()
        {
            return await Context.LiquidacoesPagamentos.ToListAsync();
        }
    }
}
