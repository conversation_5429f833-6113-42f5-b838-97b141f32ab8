﻿using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Pagamentos;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Helpers.Clientes.Models;

namespace Zin.Api.Controllers.ZinPag
{
    [Route("pagamentos")]
    [ApiController]
    public class PagamentosController(IPagamentoService pagamentoService, IPagamentoImportacaoService pagamentoImportacaoService, IExcelMappingConfigurationProvider excelMappingConfigurationProvider) : ControllerBase
    {
        public IPagamentoService _pagamentoService { get; } = pagamentoService;
        public IPagamentoImportacaoService _pagamentoImportacaoService { get; } = pagamentoImportacaoService;
        public IExcelMappingConfigurationProvider _excelMappingConfigurationProvider { get; } = excelMappingConfigurationProvider;

        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] CriaPagamentoDTO dto)
        {
            var id = await _pagamentoService.CriarPagamentoAsync(dto);
            return Ok(id);
        }

        [HttpPost("importar-excel-pagamentos")]
        public async Task<IActionResult> ImportarExcel([FromForm] ImportExcelRequestDTO request)
        {
            if (HttpContext.Items["ClienteSelecionado"] is not Cliente clienteSelecionado)
                return BadRequest("x-tenant-id inválido.");

            try
            {
                var resultado = await _pagamentoImportacaoService.ImportarAsync(request.File, clienteSelecionado.Cnpj!);
                return Ok(resultado);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("cancelar/{pagamentoId:int}")]
        public async Task<IActionResult> Cancelar(int pagamentoId)
        {
            var resultado = await _pagamentoService.CancelarPagamentoAsync(pagamentoId);
            return Ok(resultado);
        }

        [HttpPost("liquidar/{pagamentoId:int}")]
        public async Task<IActionResult> LiquidarPagamento(int pagamentoId, decimal valorPago)
        {
            var resultado = await _pagamentoService.ConfirmarPagamentoManualAsync(pagamentoId, valorPago);
            return Ok(resultado);
        }


    }
}
