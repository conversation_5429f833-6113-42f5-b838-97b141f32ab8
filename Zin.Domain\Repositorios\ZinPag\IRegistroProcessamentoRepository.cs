﻿using Zin.Domain.Entidades.ZinPag.Processamento;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface IRegistroProcessamentoRepository
    {
        Task<RegistroProcessamentoItemVersao?> BuscarPorItemVersaoIdAsync(int idItemVersao);
        Task<IEnumerable<RegistroProcessamentoItemVersao>> BuscarPorItensVersaoAsync(IEnumerable<int> idsItensVersao);
        Task<RegistroProcessamentoItemVersao?> BuscarPorItemVersaoTipoAsync(int idItemVersao, TipoProcessamento tipoProcessamento);
        Task InserirAsync(RegistroProcessamentoItemVersao registro);
        Task AtualizarAsync(RegistroProcessamentoItemVersao registro);
    }
}
