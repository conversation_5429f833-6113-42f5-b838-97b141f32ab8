﻿using Zin.Application.DTOs.Importacao;
using Zin.Application.DTOs.ItensVersoes;
using Zin.Application.Helpers;
using Zin.Application.Specifications.ItensVersoes.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Application.Specifications.ItensVersoes
{
    /// <summary>
    /// Specification para verificar se a data de autorização de um item recebido é nova
    /// Caso seja, deve ser criada uma nova versão do item.
    /// </summary>
    internal class DataAutorizacaoNovaSpecification : IItemVersaoSpecification
    {
        public DeveCriarNovaVersaoResponse IsSatisfiedBy(ICollection<ItemVersao> todasVersoes, ImportarItensDTO itemDto)
        {
            bool deveCriarNovaVersao = false;
            ItemVersao? versaoAnterior = null;

            var ultimaVersaoMesmaAutorizacao = ItemVersaoSpecificationHelper.ObterUltimaVersaoMesmaAutorizacao(
                todasVersoes,
                itemDto.DataAutorizacao,
                itemDto.CnpjFornecedor);

            if (ultimaVersaoMesmaAutorizacao != null
                && ultimaVersaoMesmaAutorizacao.DataHoraAutorizacao != itemDto.DataAutorizacao)
            {
                // A data de autorização é diferente da última versão com a mesma autorização e fornecedor,
                // criar uma nova versão
                deveCriarNovaVersao = true;
                versaoAnterior = ultimaVersaoMesmaAutorizacao;
            }

            return ItemVersaoSpecificationHelper.GeraDeveCriarNovaVersaoResponse(deveCriarNovaVersao, versaoAnterior);
        }
    }
}
