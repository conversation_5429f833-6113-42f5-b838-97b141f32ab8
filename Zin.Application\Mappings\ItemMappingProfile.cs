﻿using AutoMapper;
using Zin.Application.DTOs.Itens;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Application.Mappings
{
    public class ItemMappingProfile : Profile
    {
        public ItemMappingProfile()
        {
            // Item
            CreateMap<Item, ItemDto>();
            CreateMap<CriaItemDto, Item>()
                .ForMember(dest => dest.Versoes, opt => opt.Ignore());
            CreateMap<AtualizaItemDto, Item>()
                .ForMember(dest => dest.Versoes, opt => opt.Ignore());
        }
    }
}