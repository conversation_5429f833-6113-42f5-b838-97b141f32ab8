﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Excecoes;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class VeiculoRepository(IUnitOfWork unitOfWork) : IVeiculoRepository
    {

        private readonly IUnitOfWork _unitOfWork = unitOfWork;

        private ZinDbContext Context => _unitOfWork.Context;

        public async Task AtualizarAsync(Veiculo entidade)
        {
            Context.Veiculos.Update(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task<IEnumerable<Veiculo>> BuscarAsync(Expression<Func<Veiculo, bool>> predicado)
        {
            return await Context.Veiculos.Where(predicado).ToListAsync();
        }

        public async Task<Veiculo?> BuscarPorIdAsync(int id)
        {
            return await Context.Veiculos.FindAsync(id)
                ?? throw new EntidadeNaoEncontradaExcecao($"Veiculo com ID {id} não encontrado.");
        }

        public async Task<IEnumerable<Veiculo>> BuscaVeiculoPorPlacaAsync(string placa)
        {
            return await Context.Veiculos
                .Where(v => v.Placa == placa)
                .ToListAsync();
        }

        public async Task DeletarAsync(int id)
        {
            var entidade = await Context.Veiculos.FindAsync(id)
                ?? throw new EntidadeNaoEncontradaExcecao($"Veiculo com ID {id} não encontrado");
            Context.Veiculos.Remove(entidade);
            await _unitOfWork.CommitAsync();
        }

        public async Task DeletarVariosAsync(IEnumerable<Veiculo> entidades)
        {
            Context.Veiculos.RemoveRange(entidades);
            await _unitOfWork.CommitAsync();
        }

        public async Task<int> InserirAsync(Veiculo entidade)
        {
            await Context.Veiculos.AddAsync(entidade);
            await _unitOfWork.CommitAsync();
            return entidade.Id;
        }

        public async Task<int[]> InserirVariosAsync(IEnumerable<Veiculo> entidades)
        {
            await Context.Veiculos.AddRangeAsync(entidades);
            var result = await _unitOfWork.CommitAsync();
            return [.. entidades.Select(e => e.Id)];
        }

        public async Task<IEnumerable<Veiculo>> ListarAsync()
        {
            return await Context.Veiculos.ToListAsync();
        }
    }
}
