﻿using Serilog;
using Zin.Application.DTOs.Importacao;

namespace Zin.Application.Helpers
{
    public class ImportacaoHelper
    {
        public static bool ImportacaoValida(CriaImportacaoAgregadorDTO dto)
        {
            if (dto == null)
            {
                Log.Logger.Error("DTO de importação é nulo.");
                throw new ArgumentNullException(nameof(dto));
            }

            if (string.IsNullOrWhiteSpace(dto.Numero))
            {
                Log.Logger.<PERSON>rror("Número do agregador não pode ser nulo ou vazio.");
                return false;
            }

            if (dto.IdCliente == null || string.IsNullOrWhiteSpace(dto.IdCliente))
            {
                Log.Logger.Error("Id do cliente não pode ser nulo ou vazio.");
                return false;
            }

            if (dto.ClienteEmpresa == null || string.IsNullOrWhiteSpace(dto.ClienteEmpresa.Cnpj))
            {
                Log.Logger.Error("ClienteEmpresa ou CNPJ não pode ser nulo ou vazio.");
                return false;
            }

            if (dto.Ativos == null || !dto.Ativos.Any())
            {
                Log.Logger.Error("Ativos não pode ser nulo ou vazio.");
                return false;
            }

            if (dto.Oficinas == null || !dto.Oficinas.Any())
            {
                Log.Logger.Error("Oficinas não pode ser nulo ou vazio.");
                return false;
            }

            if (dto.Fornecedores == null || !dto.Fornecedores.Any())
            {
                Log.Logger.Error("Fornecedores não pode ser nulo ou vazio.");
                return false;
            }

            if (dto.Itens == null || !dto.Itens.Any())
            {
                Log.Logger.Error("Itens não pode ser nulo ou vazio.");
                return false;
            }
            else
            {
                foreach (var item in dto.Itens)
                {
                    if (string.IsNullOrWhiteSpace(item.Codigo))
                    {
                        Log.Logger.Error("Código do item não pode ser nulo ou vazio.");
                        return false;
                    }

                    if (item.CnpjOficina == null || string.IsNullOrWhiteSpace(item.CnpjOficina))
                    {
                        Log.Logger.Error("CNPJ da oficina não pode ser nulo ou vazio.");
                        return false;
                    }
                    else if (!dto.Oficinas.Any(o => o.Cnpj == item.CnpjOficina))
                    {
                        Log.Logger.Error("CNPJ da oficina não encontrado na lista de oficinas.");
                        return false;
                    }

                    if (item.CnpjFornecedor == null || string.IsNullOrWhiteSpace(item.CnpjFornecedor))
                    {
                        Log.Logger.Error("CNPJ do fornecedor não pode ser nulo ou vazio.");
                        return false;
                    }
                    else if (!dto.Fornecedores.Any(f => f.Cnpj == item.CnpjFornecedor))
                    {
                        Log.Logger.Error("CNPJ do fornecedor não encontrado na lista de fornecedores.");
                        return false;
                    }
                }
            }

            return true;
        }
    }
}
