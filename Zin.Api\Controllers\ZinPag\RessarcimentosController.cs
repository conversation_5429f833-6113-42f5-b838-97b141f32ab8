﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Ressarcimentos;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Api.Controllers.ZinPag
{
    [Route("ressarcimentos")]
    [ApiController]
    public class RessarcimentosController(IRessarcimentoService ressarcimentoService) : ControllerBase
    {
        public IRessarcimentoService _ressarcimentoService { get; } = ressarcimentoService;

        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] CriaRessarcimentoDto dto)
        {
            var id = await _ressarcimentoService.CriarRessarcimentoAsync(dto);
            return Ok(dto);
        }
    }
}
