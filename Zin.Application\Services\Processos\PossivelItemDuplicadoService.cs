﻿using Microsoft.Extensions.Logging;
using Zin.Application.Helpers;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.Processos
{
    public class PossivelItemDuplicadoService : IPossivelItemDuplicadoService
    {
        private readonly IItemRepository _itemRepositorio;
        private readonly IItemVersaoRepository _itemVersaoRepositorio;
        private readonly IRegistroProcessamentoRepository _registroProcessamentoRepositorio;
        private readonly ILogger<PossivelItemDuplicadoService> _logger;

        public PossivelItemDuplicadoService(
            IItemRepository itemRepositorio,
            IItemVersaoRepository itemVersaoRepositorio,
            IRegistroProcessamentoRepository registroProcessamentoRepositorio,
            ILogger<PossivelItemDuplicadoService> logger)
        {
            _itemRepositorio = itemRepositorio;
            _itemVersaoRepositorio = itemVersaoRepositorio;
            _registroProcessamentoRepositorio = registroProcessamentoRepositorio;
            _logger = logger;
        }

        public async Task ProcessarAsync(int idAgregador)
        {
            var itens = await _itemRepositorio.BuscarPorAgregadorIdAsync(idAgregador);

            foreach (var item in itens)
            {
                var versoes = await _itemVersaoRepositorio.BuscarPorItemIdAsync(item.Id);

                var autorizados = versoes.Where(v => v.TipoMovimento == TipoMovimentoItem.Autorizacao).ToList();
                var temCancelado = versoes.Any(v => v.TipoMovimento == TipoMovimentoItem.Exclusao);

                foreach (var itemversao in versoes)
                {
                    try
                    {
                        if (autorizados.Count > 1 && !temCancelado)
                        {
                            await RegistroProcessamentoHelper.RegistrarAsync(
                                itemversao,
                                TipoProcessamento.ItemDuplicado,
                                StatusProcessamento.PossivelmenteDuplicado,
                                Divergencia.PossivelItemDuplicado,
                                Condicao.SemCondicao,
                                "Item com múltiplas autorizações sem cancelamento.",
                                _registroProcessamentoRepositorio,
                                _itemVersaoRepositorio
                            );
                        }
                        else
                        {
                            await RegistroProcessamentoHelper.RegistrarAsync(
                                itemversao,
                                TipoProcessamento.ItemDuplicado,
                                StatusProcessamento.Processado,
                                Divergencia.SemDivergencia,
                                Condicao.SemCondicao,
                                "Item processado sem indício de duplicidade.",
                                _registroProcessamentoRepositorio,
                                _itemVersaoRepositorio
                            );
                        }
                    }
                    catch (Exception ex)
                    {
                        // Marca o status da versão como erro
                        StatusAtualizador.SetStatusProcessamentoItemVersao(itemversao, TipoProcessamento.ItemDuplicado, StatusProcessamento.Erro);
                        await _itemVersaoRepositorio.AtualizarAsync(itemversao);

                        // Marca o status do item como erro
                        item.StatusProcessamento = StatusProcessamento.Erro;
                        await _itemRepositorio.AtualizarAsync(item);

                        // Log do erro
                        _logger.LogError(ex, $"Erro ao processar versão {itemversao.Id} do item {item.Id} no agregador {idAgregador} no processo Possivel Item Duplicado");
                    }
                    await StatusAtualizador.AtualizarStatusGeralItemSeConcluidoAsync(item.Id, _itemRepositorio, _itemVersaoRepositorio);
                }
            }

        }
    }
}