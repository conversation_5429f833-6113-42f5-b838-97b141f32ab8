﻿using Zin.Application.DTOs.Empresas;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros
{
    public class ClienteEmpresaService(IPessoaRepository pessoaRepository) : IClienteEmpresaService
    {
        private readonly IPessoaRepository _pessoaRepository = pessoaRepository;

        public Task<int> CriaClienteEmpresaAsync(CriaClienteEmpresaDto criaEmpresaDto)
        {
            if (criaEmpresaDto == null)
                throw new ArgumentNullException(nameof(criaEmpresaDto), "O DTO de criação de empresa não pode ser nulo.");

            return _pessoaRepository.InserirAsync(new ClienteEmpresa
            {
                RazaoSocial = criaEmpresaDto.RazaoSocial,
                NomeFantasia = criaEmpresaDto.NomeFantasia,
                Cnpj = criaEmpresaDto.Cnpj,
                Matriz = criaEmpresaDto.Matriz,
                TipoPessoa = TipoPessoa.ClienteEmpresa                
            });
        }
    }
}
