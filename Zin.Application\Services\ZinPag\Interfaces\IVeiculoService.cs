﻿using Zin.Application.DTOs.Veiculos;


namespace Zin.Application.Services.ZinPag.Interfaces
{
    public interface IVeiculoService
    {
        Task<IEnumerable<VeiculoDto>> ListarVeiculosAsync();

        Task<VeiculoDto?> ObterVeiculoPorIdAsync(int id);

        Task<int> CriarVeiculoAsync(CriaVeiculoDto dto);

        Task AtualizaVeiculoAsync(int id, AtualizaVeiculoDto dto);

        Task RemoveVeiculoAsync(int id);

        Task AtualizaStatusVeiculo(AtualizaStatusVeiculoDto dto);

    }
}
