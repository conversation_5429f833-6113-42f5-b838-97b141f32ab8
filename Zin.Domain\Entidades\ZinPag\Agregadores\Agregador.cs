﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.Cadastros.Pessoas;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Documentos;
using Zin.Domain.Entidades.ZinPag.Itens;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;

namespace Zin.Domain.Entidades.ZinPag.Agregadores
{
    [Table("agregadores", Schema = "zinpag")]
    public class Agregador
    {
        // 1. Chave primária
        [Key]
        [Column("id_agregador")]
        public int Id { get; set; }

        // 2. <PERSON>ves estrangeira<PERSON>
        [Column("id_cliente_empresa")]
        public required int IdClienteEmpresa { get; set; }

        // 3. Propriedades simples
        [Column("tipo_agregador")]
        public required TipoAgregador TipoAgregador { get; set; }

        [Column("numero")]
        [StringLength(100)]
        public required string Numero { get; set; }

        [Column("status_processamento")]
        public StatusProcessamento StatusProcessamento { get; set; }

        // 4. Propriedades de navegação
        [ForeignKey(nameof(IdClienteEmpresa))]
        public ClienteEmpresa? ClienteEmpresa { get; set; }

        public ICollection<Ativo> Ativos { get; set; } = [];

        public ICollection<Item> Itens { get; set; } = [];

        public ICollection<Documento> Documentos { get; set; } = [];

        public ICollection<Movimentacao> Movimentacoes { get; set; } = [];
    }
}