﻿using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Itens;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Api.Controllers.ZinPag
{
    [ApiController]
    [Route("api/itens")]
    public class ItensController(IItemService itemService) : ControllerBase
    {
        [HttpPost]
        public async Task<IActionResult> <PERSON><PERSON>r([FromBody] CriaItemDto dto)
            => Ok(await itemService.CriarItemAsync(dto));

        [HttpGet("por-agregador/{idAgregador}")]
        public async Task<IActionResult> ListarPorAgregador(int idAgregador)
            => Ok(await itemService.ListarItensPorAgregadorAsync(idAgregador));

        [HttpGet("{id}")]
        public async Task<IActionResult> Obter(int id)
            => Ok(await itemService.ObterItemPorIdAsync(id));
    }
}
