﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Ativos
{
    [Table("veiculos", Schema = "zinpag")]
    public class Veiculo : Ativo
    {
        [Column("placa")]
        [StringLength(10)]
        public required string Placa { get; set; }

        [Column("chassi")]
        [StringLength(30)]
        public string? Chassi { get; set; } 

        [Column("renavam")]
        [StringLength(20)]
        public string? Renavam { get; set; }

        [Column("modelo")]
        [StringLength(100)]
        public string? Modelo { get; set; }

        [Column("ano_fabricacao")]
        public int? AnoFabricacao { get; set; }

        [Column("ano_modelo")]
        public int? AnoModelo { get; set; }

        [Column("status")]
        public StatusVeiculo StatusVeiculo { get; set; }
        public ICollection<VeiculoOficina> Oficinas { get; set; } = [];
    }
}
