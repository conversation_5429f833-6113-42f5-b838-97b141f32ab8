﻿using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Ativos;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class MovimentacoesRepository(IUnitOfWork unitOfWork) : IMovimentacoesRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext Context => _unitOfWork.Context;

        public async Task<Movimentacao?> BuscarDetalhesMovimentacaoAsync(int id)
        {
            return await Context.Movimentacoes
                .Include(m => m.Documento)
                    .ThenInclude(d => d.Emitente)
                        .ThenInclude(e => e.DadosBancarios)
                .Include(m => m.Agregador)
                    .ThenInclude(a => a.Ativos)
                        .ThenInclude(a => (a as Veiculo).Oficinas) // EF suporta esse padrão
                            .ThenInclude(vo => vo.Oficina)
                .FirstOrDefaultAsync(m => m.IdMovimentacao == id);
        }

        public Task AtualizarAsync(Movimentacao entidade)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Movimentacao>> BuscarAsync(Expression<Func<Movimentacao, bool>> predicado)
        {
            throw new NotImplementedException();
        }

        public Task<Movimentacao?> BuscarPorIdAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<Movimentacao> entidades)
        {
            throw new NotImplementedException();
        }

        public Task<int> InserirAsync(Movimentacao entidade)
        {
            throw new NotImplementedException();
        }

        public Task<int[]> InserirVariosAsync(IEnumerable<Movimentacao> entidades)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<Movimentacao>> ListarAsync()
        {
            throw new NotImplementedException();
        }
    }
}
