using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Zin.Application.Configuration;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Application.DTOs.Notifications;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Cadastros;

namespace Zin.Application.Services.Cadastros;

public class FornecedorService : IFornecedorService
{
    private readonly IPessoaRepository _pessoaRepository;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<FornecedorService> _logger;
    private readonly NotificationServiceConfig _notificationServiceConfig;

    public FornecedorService(
        IPessoaRepository pessoaRepository,
        IHttpClientFactory httpClientFactory,
        ILogger<FornecedorService> logger,
        IOptions<NotificationServiceConfig> notificationServiceConfig)
    {
        _pessoaRepository = pessoaRepository;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
        _notificationServiceConfig = notificationServiceConfig.Value;
    }

    public async Task SolicitarDadosBancariosAsync(int fornecedorId)
    {
        var fornecedor = await _pessoaRepository.BuscarComContatosPorIdAsync(fornecedorId);

        if (fornecedor == null)
        {
            _logger.LogWarning("Fornecedor com ID {FornecedorId} não encontrado.", fornecedorId);
            throw new Exception($"Fornecedor com ID {fornecedorId} não encontrado.");
        }

        var emailContato = fornecedor.PessoasContatos
            .FirstOrDefault(pc => pc.Contato.TipoContato == TipoContato.Email && pc.Principal)?.Contato;

        if (emailContato == null)
        {
            _logger.LogError("Fornecedor com ID {FornecedorId} não possui um e-mail principal de contato.", fornecedorId);
            throw new Exception("Fornecedor não possui um e-mail principal de contato.");
        }

        var request = new NotificacaoRequest
        {
            Recipient = emailContato.Valor,
            Subject = $"Atualização de Dados Bancários.",
            Message = $"<p>Prezado fornecedor,</p><p>Solicitamos que por gentileza atualize seus dados bancários.</p><p>Atenciosamente,</p><p>Sistema Zin.</p>",
            Channel = NotificacaoCanal.Email
        };

        var httpClient = _httpClientFactory.CreateClient();
        try
        {
            var endpoint = new Uri(new Uri(_notificationServiceConfig.BaseUrl), "api/notificacao");
            var response = await httpClient.PostAsJsonAsync(endpoint, request);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Solicitação de dados bancários para o fornecedor {FornecedorId} enviada com sucesso.", fornecedorId);
            }
            else
            {
                var responseBody = await response.Content.ReadAsStringAsync();
                _logger.LogError("Falha ao enviar solicitação de dados bancários para o fornecedor {FornecedorId}. Status: {StatusCode}, Response: {ResponseBody}", 
                    fornecedorId, response.StatusCode, responseBody);
                throw new Exception("Falha ao enviar a notificação.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao comunicar com o serviço de notificação para o fornecedor {FornecedorId}.", fornecedorId);
            throw;
        }
    }
}
