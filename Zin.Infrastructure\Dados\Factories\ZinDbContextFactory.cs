﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Zin.Helpers.Clientes;

namespace Zin.Infrastructure.Dados.Factories
{
    public class ZinDbContextFactory(
        IHttpContextAccessor httpContextAccessor,
        IClienteConnectionService clienteConnectionService) : IDbContextFactory<ZinDbContext>
    {
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        private readonly IClienteConnectionService _clienteConnectionService = clienteConnectionService;

        public ZinDbContext CreateDbContext()
        {            
            var cliente = ClienteSelecionadoHelper.BuscarClienteSelecionado(_httpContextAccessor.HttpContext!) 
                ?? throw new InvalidOperationException("Cliente selecionado não encontrado no contexto HTTP. " +
                    "Certifique-se de que o middleware ClienteFromClaimMiddleware foi executado corretamente.");

            if (string.IsNullOrEmpty(cliente.BaseDados))
            {
                throw new InvalidOperationException("O cliente selecionado não possui uma base de dados configurada. " +
                    "Certifique-se de que o middleware ClienteFromClaimMiddleware foi executado corretamente " +
                    "e que o cliente tem uma base de dados definida.");
            }

            var nomeBaseDados = cliente.BaseDados;
            var connectionString = _clienteConnectionService.BuscaClienteConnectionString(nomeBaseDados!);

            var optionsBuilder = new DbContextOptionsBuilder<ZinDbContext>();
            optionsBuilder.UseNpgsql(connectionString);

            return new ZinDbContext(optionsBuilder.Options);
        }
    }
}
