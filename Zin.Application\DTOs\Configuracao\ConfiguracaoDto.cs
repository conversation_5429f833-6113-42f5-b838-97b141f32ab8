﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;

namespace Zin.Application.DTOs.Configuracao
{
    public class ConfiguracaoDto
    {
        public Guid Id { get; set; }
        public string Nome { get; set; } = string.Empty;
        public TipoProcessamento TipoProcessamento { get; set; }
        public TipoConfiguracao TipoConfiguracao { get; set; }
        public bool Ativo { get; set; }
        public DateTime CriadoEm { get; set; }
        public DateTime AtualizadoEm { get; set; }
        public string CriadoPor { get; set; } = string.Empty;
        public string AlteradoPor { get; set; } = string.Empty;
        public List<RegraDto> Regras { get; set; } = new();
    }
}
