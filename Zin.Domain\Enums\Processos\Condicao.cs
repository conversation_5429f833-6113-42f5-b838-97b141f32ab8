﻿using System.ComponentModel;

namespace Zin.Domain.Enums.Processos
{
    public enum Condicao
    {
        /// <summary>Condição desconhecida ou não informada.</summary>
        Desconhecido = 0,

        /// <summary>Sem condição.</summary>
        SemCondicao = 100,


        // ---- CONDIÇÕES DE PAGAMENTO ----

        /// <summary>Condição aprovada para pagamento.</summary>
        [Description("Aprovado para pagamento")]
        AprovadoPagamento = 200,

        /// <summary>Não completou 35 dias após a entrega do item.</summary>
        [Description("Menos de 35 dias após entrega")]
        MenosDe35DiasEntrega = 210,

        /// <summary>Veículo não entregue ou concluído.</summary>
        [Description("Veículo não entregue ou concluído")]
        VeiculoNaoEntregue = 220,

        /// <summary>Nota fiscal não anexada.</summary>
        [Description("Nota fiscal não anexada")]
        NotaFiscalNaoAnexada = 230,

        /// <summary>Item Versão status não entregue.</summary>
        [Description("Item Versão não entregue")]
        ItemVersaoNaoEntregue = 240,


        // ---- CONDIÇÕES DE RESSARCIMENTO ----

        /// <summary>Condição aprovada para ressarcimento.</summary>
        [Description("Aprovado para ressarcimento")]
        AprovadoRessarcimento = 300,

        /// <summary>Não completou 15 dias após a exclusão do item.</summary>
        [Description("Menos de 15 dias após exclusão")]
        MenosDe15DiasExclusao = 310
    }
}