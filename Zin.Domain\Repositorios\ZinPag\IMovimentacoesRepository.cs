﻿using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Entidades.ZinPag.Movimentacoes;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface IMovimentacoesRepository : IRepositoryBase<Movimentacao, int>
    {
        Task<Movimentacao?> BuscarDetalhesMovimentacaoAsync(int id);
        Task<bool> ExistemMovimentacoesPorAgregadorExcluindoMovimentacaoAsync(int idAgregador, int movIdExcluir);
    }
}