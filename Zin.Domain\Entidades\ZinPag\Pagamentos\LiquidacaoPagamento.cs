using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Pagamentos
{
    [Table("liquidacoes_pagamentos", Schema = "zinpag")]
    public class LiquidacaoPagamento
    {
        [Key]
        [Column("id_liquidacao_pagamento")]
        public int Id { get; set; }

        [Column("id_pagamento")]
        public int IdPagamento { get; set; }

        [Column("status_pagamento")]
        public StatusPagamento StatusPagamento { get; set; }

        [Column("data")]
        public DateTime Data { get; set; }

        [Column("valor_pago", TypeName = "decimal(10,2)")]
        public decimal ValorPago { get; set; }

        [ForeignKey(nameof(IdPagamento))]
        public Pagamento Pagamento { get; set; }
    }
}
