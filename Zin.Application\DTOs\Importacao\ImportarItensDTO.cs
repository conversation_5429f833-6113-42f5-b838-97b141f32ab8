﻿using Zin.Domain.Enums;

namespace Zin.Application.DTOs.Importacao
{
    public class ImportarItensDTO
    {
        public TipoMovimentoItem TipoMovimentoItem { get; set; }

        public int IdItemPedidoFornecedor { get; set; }
        public string Codigo { get; set; }
        public string Descricao { get; set; }

        public DateTime DataCriacao { get; set; }
        public DateTime DataAutorizacao { get; set; }
        public DateTime DataMovimento { get; set; }
        public int IdFornecedor { get; set; }

        public string CnpjFornecedor { get; set; }

        public int Quantidade { get; set; }
        public decimal ValorUnitario { get; set; }
        public decimal ValorTotal { get; set; }
        public DateTime? DataEntrega { get; set; }

        public IList<ImportarDocumentoDTO> DocumentosRelacionados { get; set; }

        public string CnpjOficina { get; set; }
        public string VeiculoPlaca { get; set; }


    }
}
