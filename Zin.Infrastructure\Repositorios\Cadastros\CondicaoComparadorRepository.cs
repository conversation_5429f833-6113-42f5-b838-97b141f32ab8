﻿using Microsoft.EntityFrameworkCore;
using Zin.Domain.Entidades.Cadastros.Condicoes;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Infrastructure.Repositorios.Cadastros
{
    public class CondicaoComparadorRepository(IUnitOfWork unitOfWork) : ICondicaoComparadorRepository
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private ZinDbContext _context => _unitOfWork.Context;

        public async Task<Configuracao?> GetConfiguracaoComRegrasAsync(Guid id)
            => await _context.Configuracoes
                .Include(x => x.Regras)
                .FirstOrDefaultAsync(x => x.Id == id);
    }
}
