﻿using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using Zin.Application.Helpers;
using Zin.Application.Services.Processos;
using Zin.Domain.Enums;
using Zin.Domain.Enums.Processos;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Services.ZinPag
{
    public class ProcessosService : IProcessosService
    {
        private readonly IAgregadorRepository _agregadorRepositorio;
        private readonly IItemRepository _itemRepositorio;
        private readonly IItemVersaoRepository _itemVersaoRepositorio;

        private readonly PossivelItemDuplicadoService _itemDuplicadoService;
        private readonly PossivelPagamentoDuplicadoService _pagamentoDuplicadoService;
        private readonly PossivelPagamentoService _possivelPagamentoService;
        private readonly PossivelRessarcimentoService _possivelRessarcimentoService;
        private readonly ILogger<ProcessosService> _logger;

        public ProcessosService(
            IAgregadorRepository agregadorRepositorio,
            IItemRepository itemRepositorio,
            IItemVersaoRepository itemVersaoRepositorio,
            PossivelItemDuplicadoService itemDuplicadoService,
            PossivelPagamentoDuplicadoService pagamentoDuplicadoService,
            PossivelPagamentoService possivelPagamentoService,
            PossivelRessarcimentoService possivelRessarcimentoService,
            ILogger<ProcessosService> logger
        )
        {
            _agregadorRepositorio = agregadorRepositorio;
            _itemRepositorio = itemRepositorio;
            _itemVersaoRepositorio = itemVersaoRepositorio;
            _itemDuplicadoService = itemDuplicadoService;
            _pagamentoDuplicadoService = pagamentoDuplicadoService;
            _possivelPagamentoService = possivelPagamentoService;
            _possivelRessarcimentoService = possivelRessarcimentoService;
            _logger = logger;
        }

        public async Task ProcessaItemDuplicadoAsync()
        {
            var agregadores = await _agregadorRepositorio.BuscarPorStatusAsync(StatusProcessamento.AProcessar);
            foreach (var agregador in agregadores)
            {
                try
                {
                    await _itemDuplicadoService.ProcessarAsync(agregador.Id);
                    await StatusAtualizador.AtualizarStatusGeralAgregadorSeConcluidoAsync(
                        agregador.Id, _agregadorRepositorio, _itemRepositorio, _itemVersaoRepositorio
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Erro ao processar ItemDuplicado para agregador {agregador.Id}");
                    await _agregadorRepositorio.AtualizarStatusAsync(agregador.Id, StatusProcessamento.Erro);
                }
            }
        }

        public async Task ProcessaPagamentoDuplicadoAsync()
        {
            var agregadores = await _agregadorRepositorio.BuscarPorStatusAsync(StatusProcessamento.AProcessar);
            foreach (var agregador in agregadores)
            {
                try
                {
                    await _pagamentoDuplicadoService.ProcessarAsync(agregador.Id);
                    await StatusAtualizador.AtualizarStatusGeralAgregadorSeConcluidoAsync(
                        agregador.Id, _agregadorRepositorio, _itemRepositorio, _itemVersaoRepositorio
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Erro ao processar PagamentoDuplicado para agregador {agregador.Id}");
                    await _agregadorRepositorio.AtualizarStatusAsync(agregador.Id, StatusProcessamento.Erro);
                }
            }
        }

        public async Task ProcessaPossivelPagamentoAsync()
        {
            var agregadores = await _agregadorRepositorio.BuscarPorStatusAsync(StatusProcessamento.AProcessar);
            foreach (var agregador in agregadores)
            {
                try
                {
                    await _possivelPagamentoService.ProcessarAsync(agregador.Id);
                    await StatusAtualizador.AtualizarStatusGeralAgregadorSeConcluidoAsync(
                        agregador.Id, _agregadorRepositorio, _itemRepositorio, _itemVersaoRepositorio
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Erro ao processar PossivelPagamento para agregador {agregador.Id}");
                    await _agregadorRepositorio.AtualizarStatusAsync(agregador.Id, StatusProcessamento.Erro);
                }
            }
        }

        public async Task ProcessaPossivelRessarcimentoAsync()
        {
            var agregadores = await _agregadorRepositorio.BuscarPorStatusAsync(StatusProcessamento.AProcessar);
            foreach (var agregador in agregadores)
            {
                try
                {
                    await _possivelRessarcimentoService.ProcessarAsync(agregador.Id);
                    await StatusAtualizador.AtualizarStatusGeralAgregadorSeConcluidoAsync(
                        agregador.Id, _agregadorRepositorio, _itemRepositorio, _itemVersaoRepositorio
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Erro ao processar PossivelRessarcimento para agregador {agregador.Id}");
                    await _agregadorRepositorio.AtualizarStatusAsync(agregador.Id, StatusProcessamento.Erro);
                }
            }
        }
    }
}