﻿using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Api.Controllers.ZinPag
{
    [ApiController]
    [Route("importacoes")]
    public class ImportacaoController(IImportacaoService importacaoService) : ControllerBase
    {         
       
        [HttpPost("importar")]
        public async Task<IActionResult> ImportaAgregador([FromBody] CriaImportacaoAgregadorDTO dto)
        {
            var idImportacao = await importacaoService.ImportarAgregadorAsync(dto);
            return Ok(idImportacao);
        }

        [HttpPost("processar")]
        public async Task<IActionResult> ProcessarAgregador()
        {
            await importacaoService.ProcessaImportacaoAgregadorAsync();
            return Ok();
        }
    }
}
