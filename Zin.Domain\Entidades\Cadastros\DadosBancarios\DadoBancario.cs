﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.Cadastros.DadosBancarios
{
    [Table("dados_bancarios", Schema = "cadastros")]
    public class DadoBancario
    {
        [Key]
        [Column("id_dado_bancario")]
        public int Id { get; set; }

        [Column("banco")]
        [StringLength(50)]
        public required string Banco { get; set; }

        [Column("agencia")]
        [StringLength(10)]
        public string? Agencia { get; set; }

        [Column("conta")]
        [StringLength(20)]
        public required string Conta { get; set; }

        [Column("tipo_conta")]
        public TipoConta TipoConta { get; set; }

        [Column("titular")]
        [StringLength(100)]
        public required string Titular { get; set; }

        [Column("cpf_cnpj_titular")]
        [StringLength(14, MinimumLength = 11)]
        public required string CpfCnpjTitular { get; set; }

        [Column("principal")]
        public bool Principal { get; set; }
    }
}
