﻿using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Configuracao;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Api.Controllers.Cadastros
{
    [ApiController]
    [Route("api/zinpag/configuracoes")]
    public class ConfiguracaoController : ControllerBase
    {
        private readonly IConfiguracaoService _service;

        public ConfiguracaoController(IConfiguracaoService service)
        {
            _service = service;
        }

        /// <summary>
        /// Lista todas as configurações.
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Listar()
            => Ok(await _service.ListarConfiguracaoAsync());

        /// <summary>
        /// Busca uma configuração por ID.
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> Buscar(Guid id)
        {
            var configuracao = await _service.ObterConfiguracaPorIdAsync(id);
            if (configuracao == null) return NotFound();
            return Ok(configuracao);
        }

        /// <summary>
        /// cria uma nova configuração.
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] CriaConfiguracaoDto dto)
        {
            var created = await _service.CriarConfiguracaoAsync(dto);
            return CreatedAtAction(nameof(Buscar), new { id = created.Id }, created);
        }

        /// <summary>
        /// Atualiza uma configuração existente.
        /// </summary>
        [HttpPut("{id}")]
        public async Task<IActionResult> Atualizar(Guid id, [FromBody] AtualizaConfiguracaoDto dto)
        {
            if (id != dto.Id) return BadRequest();
            await _service.AtualizarConfiguracaoAsync(id, dto);
            return NoContent();
        }

        /// <summary>
        /// Remove uma configuração por ID.
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Deletar(Guid id)
        {
            await _service.RemoverConfiguracaoAsync(id);
            return NoContent();
        }


        // --- Regras ---
        /// <summary>
        /// Busca uma regra de uma configuração por id.
        [HttpGet("{id}/regras/{idRegra}")]
        public async Task<IActionResult> BuscaRegra(Guid id, Guid idRegra)
        {
            var regra = await _service.ObterRegraPorIdAsync(idRegra);
            if (regra == null) return NotFound();
            return Ok(regra);
        }

        /// <summary>
        /// Atualiza uma regra de uma configuração.
        /// </summary>
        [HttpPut("{id}/regras/{idRegra}")]
        public async Task<IActionResult> AtualizaRegra(Guid id, Guid idRegra, [FromBody] AtualizaRegraDto dto)
        {
            if (idRegra != dto.Id) return BadRequest();
            dto.IdConfiguracao = id;
            await _service.AtualizarRegraAsync(id, dto);
            return NoContent();
        }

        /// <summary>
        /// Remove uma regra de uma configuração por ID.
        /// </summary>
        [HttpDelete("{id}/regras/{idRegra}")]
        public async Task<IActionResult> DeletaRegra(Guid id, Guid idRegra)
        {
            await _service.RemoverRegraAsync(idRegra);
            return NoContent();
        }
    }
}