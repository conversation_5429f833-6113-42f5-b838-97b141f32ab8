﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Entidades.ZinPag.Agregadores;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.Importacoes
{
    [Table("importacoes", Schema = "importacoes")]
    public class ImportacaoAgregador
    {
        [Key]
        [Column("id_importacao")]
        public int Id { get; set; }

        [Column("dados_agregador")]
        public string DadosAgregador { get; set; }

        [Column("status")]
        public StatusImportacao Status { get; set; }

        [Column("data_importacao")]
        public DateTime DataImportacao { get; set; }

        [Column("data_processamento")]
        public DateTime? DataProcessamento { get; set; }

        [Column("id_agregador")]
        public int IdAgregador { get; set; }

        [Column("id_cliente")]
        public string IdCliente { get; set; }


    }
}
