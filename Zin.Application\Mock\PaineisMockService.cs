﻿using Zin.Application.DTOs.Paineis;
using Zin.Application.Services.ZinPag.Interfaces;

namespace Zin.Application.Mock
{
    public class PaineisMockService : IPaineisService
    {
        public Task<PainelPrincipalDto> ObterPainelPrincipalAsync()
        {
            var painel = new PainelPrincipalDto
            {
                AutorizadoTotal = new PainelPrincipalValorDto
                {
                    Valor = 142250.94m
                },
                Exclusoes = new PainelPrincipalExclusoesDto
                {
                    Valor = 2000.00m,
                    Progresso = 0.75,
                    Notas = 5
                },
                NfsPagas = new PainelPrincipalNfsPagasDto
                {
                    Valor = 15145.31m,
                    Progresso = 0.9,
                    Notas = 2,
                    TotalAPagar = new PainelPrincipalTotalAPagarDto
                    {
                        Valor = 8000.00m
                    },
                    Incluidas = new PainelPrincipalIncluidasDto
                    {
                        Valor = 6000.00m
                    },
                    PendenteExclusao = new PainelPrincipalPendentesExclusaoDto
                    {
                        Valor = 2000.00m
                    }
                },
                Ressarcimentos = new PainelPrincipalRessarcimentosDto
                {
                    Valor = 0m,
                    Progresso = 0,
                    Notas = 0,
                    Realizados = new PainelPrincipalRealizadosDto
                    {
                        Valor = 0
                    },
                    Pendentes = new PainelPrincipalPendentesDto
                    {
                        Valor = 0
                    },
                    NfsPendentesExclusao = new PainelPrincipalNfsPendentesExclusaoDto
                    {
                        Valor = 0
                    }
                }
            };
            return Task.FromResult(painel);
        }
    }
}
