﻿using Microsoft.AspNetCore.Mvc;
using Zin.Application.DTOs.Veiculos;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Api.Controllers.ZinPag
{
    [Route("veiculos")]
    [ApiController]
    public class VeiculosController(IVeiculoService veiculoService) : ControllerBase
    {
        public IVeiculoService _veiculoService { get; } = veiculoService;

        [HttpPut("{id}")]
        public async Task<IActionResult> Atualizar(int id, [FromBody] AtualizaVeiculoDto dto)
        {
            if (dto == null || id != dto.Id)
                return BadRequest("Dados inválidos.");

            await _veiculoService.AtualizaVeiculoAsync(id, dto);
            return NoContent();
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> Buscar(int id)
        {
            var veiculo = await _veiculoService.ObterVeiculoPorIdAsync(id);
            if (veiculo == null)
                return NotFound();
            return Ok(veiculo);
        }

        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] CriaVeiculoDto dto)
        {
            var id = await _veiculoService.CriarVeiculoAsync(dto);
            return Ok(id);
        }

        [HttpPost("{id}")]
        public async Task<IActionResult> AtualizaStatusVeiculo(int id, [FromBody] AtualizaStatusVeiculoDto dto)
        {
            if (id != dto.Id)
            {
                return BadRequest("Id do veículo não corresponde");
            }

            await _veiculoService.AtualizaStatusVeiculo(dto);
            return NoContent();
        }


        [HttpDelete("{id}")]
        public async Task<IActionResult> Deletar(int id)
        {
            await _veiculoService.RemoveVeiculoAsync(id);
            return NoContent();
        }

        [HttpGet]
        public async Task<IActionResult> Listar()
        {
            var veiculos = await _veiculoService.ListarVeiculosAsync();
            return Ok(veiculos);
        }

    }
}
