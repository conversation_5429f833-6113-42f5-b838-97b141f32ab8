using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Zin.Domain.Entidades.ZinPag.Itens;

namespace Zin.Domain.Repositorios.ZinPag
{
    public interface IItemRepository
    {
        Task<int> InserirAsync(Item item);
        Task AtualizarAsync(Item item);
        Task<Item?> BuscarPorIdAsync(int id);
        Task<Item?> BuscarPorCodigoAsync(string codigo);
        Task<IEnumerable<Item>> ListarAsync();
        Task<IEnumerable<Item>> BuscarAsync(Expression<Func<Item, bool>> predicate);
        Task DeletarAsync(int id);
        Task<IEnumerable<Item>> BuscarPorAgregadorIdAsync(int agregadorId);
        Task<IEnumerable<Item>> BuscarItensComVersoesPorAgregadorExcluindoMovimentacaoAsync(int idAgregador, int movIdExcluir);
        Task<IEnumerable<Item>> BuscarItensComVersoesPorMovimentacaoAsync(int idMovimentacao);
    }
}
